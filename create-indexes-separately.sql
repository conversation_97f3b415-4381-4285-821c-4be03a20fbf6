-- Optional: Create Vector Indexes for Better Performance
-- Run these ONE AT A TIME if you want to add indexes for better query performance
-- Only run if your dataset is large enough to benefit from indexing

-- IMPORTANT: Run each CREATE INDEX statement separately, not all at once
-- This helps avoid memory issues

-- Index for <PERSON>'s mental health documents
-- Run this first, wait for completion
CREATE INDEX CONCURRENTLY IF NOT EXISTS mental_health_documents_embedding_idx 
ON mental_health_documents USING ivfflat (embedding vector_cosine_ops) WITH (lists = 5);

-- Index for <PERSON>'s coding database  
-- Run this second, wait for completion
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS coding_database_embedding_idx 
-- ON coding_database USING ivfflat (embedding vector_cosine_ops) WITH (lists = 5);

-- Index for <PERSON>'s job search database
-- Run this third, wait for completion  
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS job_search_database_embedding_idx 
-- ON job_search_database USING ivfflat (embedding vector_cosine_ops) WITH (lists = 5);

-- Instructions:
-- 1. Uncomment and run ONE index creation at a time
-- 2. Wait for each to complete before running the next
-- 3. Use CONCURRENTLY to avoid locking the table
-- 4. Use lists = 5 (smaller than default) to reduce memory usage
-- 5. If you still get memory errors, you can skip indexes entirely
--    Your queries will work but may be slower on large datasets
