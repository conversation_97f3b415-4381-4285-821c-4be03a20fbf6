-- Setup Chat Memory Tables for All Agents
-- Run this in your Supabase SQL Editor

-- Create n8n_chat_histories table for Li and Orion
CREATE TABLE IF NOT EXISTS n8n_chat_histories (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    agent_name VARCHAR(50) NOT NULL,
    message_type VARCHAR(20) NOT NULL CHECK (message_type IN ('human', 'ai')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_n8n_chat_histories_session_id ON n8n_chat_histories(session_id);
CREATE INDEX IF NOT EXISTS idx_n8n_chat_histories_agent_name ON n8n_chat_histories(agent_name);
CREATE INDEX IF NOT EXISTS idx_n8n_chat_histories_created_at ON n8n_chat_histories(created_at);

-- Create individual chat history tables if they don't exist
-- (These should already exist, but let's ensure they're properly set up)

CREATE TABLE IF NOT EXISTS amanda_chat_histories (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    message_type VARCHAR(20) NOT NULL CHECK (message_type IN ('human', 'ai')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS cody_chat_histories (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    message_type VARCHAR(20) NOT NULL CHECK (message_type IN ('human', 'ai')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS scout_chat_histories (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    message_type VARCHAR(20) NOT NULL CHECK (message_type IN ('human', 'ai')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS rob_chat_histories (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    message_type VARCHAR(20) NOT NULL CHECK (message_type IN ('human', 'ai')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for all chat history tables
CREATE INDEX IF NOT EXISTS idx_amanda_chat_histories_session_id ON amanda_chat_histories(session_id);
CREATE INDEX IF NOT EXISTS idx_cody_chat_histories_session_id ON cody_chat_histories(session_id);
CREATE INDEX IF NOT EXISTS idx_scout_chat_histories_session_id ON scout_chat_histories(session_id);
CREATE INDEX IF NOT EXISTS idx_rob_chat_histories_session_id ON rob_chat_histories(session_id);

-- Grant necessary permissions
GRANT ALL ON n8n_chat_histories TO anon, authenticated;
GRANT ALL ON amanda_chat_histories TO anon, authenticated;
GRANT ALL ON cody_chat_histories TO anon, authenticated;
GRANT ALL ON scout_chat_histories TO anon, authenticated;
GRANT ALL ON rob_chat_histories TO anon, authenticated;

-- Grant sequence permissions
GRANT USAGE, SELECT ON SEQUENCE n8n_chat_histories_id_seq TO anon, authenticated;
GRANT USAGE, SELECT ON SEQUENCE amanda_chat_histories_id_seq TO anon, authenticated;
GRANT USAGE, SELECT ON SEQUENCE cody_chat_histories_id_seq TO anon, authenticated;
GRANT USAGE, SELECT ON SEQUENCE scout_chat_histories_id_seq TO anon, authenticated;
GRANT USAGE, SELECT ON SEQUENCE rob_chat_histories_id_seq TO anon, authenticated;

-- Create a function to clean up old chat histories (optional)
CREATE OR REPLACE FUNCTION cleanup_old_chat_histories(days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Clean up n8n_chat_histories
    DELETE FROM n8n_chat_histories 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Clean up individual agent histories
    DELETE FROM amanda_chat_histories 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    DELETE FROM cody_chat_histories 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    DELETE FROM scout_chat_histories 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    DELETE FROM rob_chat_histories 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    RETURN deleted_count;
END;
$$;

-- Grant execute permission on cleanup function
GRANT EXECUTE ON FUNCTION cleanup_old_chat_histories TO anon, authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Chat memory tables setup completed!';
    RAISE NOTICE '📋 Tables created/verified:';
    RAISE NOTICE '   - n8n_chat_histories (for Li and Orion)';
    RAISE NOTICE '   - amanda_chat_histories';
    RAISE NOTICE '   - cody_chat_histories';
    RAISE NOTICE '   - scout_chat_histories';
    RAISE NOTICE '   - rob_chat_histories';
    RAISE NOTICE '🔧 Indexes and permissions configured';
    RAISE NOTICE '🧹 Cleanup function available: SELECT cleanup_old_chat_histories(30);';
END $$;
