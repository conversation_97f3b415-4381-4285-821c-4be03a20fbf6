# RAG Agents Supabase Connection Troubleshooting Guide

## Issues Identified

Based on the diagnostic tests, here are the specific issues with your RAG agents' Supabase connections:

### 1. ✅ **Environment Variables** - WORKING
- All required Supabase environment variables are properly set
- Basic Supabase connection is successful

### 2. ✅ **Database Tables** - WORKING
- `mental_health_documents` (<PERSON>) - Accessible with data
- `coding_database` (<PERSON>) - Accessible with data  
- `job_search_database` (<PERSON>) - Accessible with data
- All chat memory tables are working

### 3. ⚠️ **Vector Embedding Dimensions** - PARTIALLY FIXED
- **Issue**: Your RPC functions were expecting 1536-dimensional embeddings, but your Ollama `nomic-embed-text` model produces 768-dimensional embeddings
- **Status**: <PERSON> and <PERSON> RPC functions now work with 768 dimensions
- **Action**: The SQL fix script addresses this

### 4. ❌ **Missing RPC Function** - NEEDS FIX
- **Issue**: <PERSON>'s `match_job_search_database` RPC function is missing
- **Impact**: <PERSON> cannot search his knowledge base
- **Action**: The SQL fix script creates this function

## Solution Steps

### Step 1: Run the SQL Fix Script
**Option A: Minimal Fix (Recommended)**
1. Open your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of `fix-supabase-minimal.sql`
4. Execute the script

**Option B: Full Fix (if you have sufficient memory)**
1. Use `fix-supabase-setup.sql` if the minimal version doesn't work
2. If you get memory errors (54000), use the minimal version instead

**If you get "memory required is 33 MB, maintenance_work_mem is 32 MB" error:**
- This is a Supabase memory limitation for index creation
- Use the minimal fix script instead
- Indexes can be added later if needed for performance

### Step 2: Verify the Fix
Run the verification script:
```bash
node verify-rag-agents.js
```

### Step 3: Update Your Code (if needed)
The Genkit tools in `src/ai/tools/supabase.ts` should work correctly after the SQL fixes, but ensure they're using the right embedding model.

## Technical Details

### Embedding Model Configuration
Your n8n agents are configured to use:
- **Model**: `nomic-embed-text:latest` (Ollama)
- **Dimensions**: 768
- **Provider**: Ollama API

### RPC Function Signatures
After the fix, all RPC functions will have this signature:
```sql
FUNCTION match_[table_name](
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
```

### Expected Behavior After Fix
1. **Amanda**: Can search mental health documents using semantic similarity
2. **Cody**: Can search coding documentation and solutions
3. **Scott**: Can search job search tips and resume advice
4. All agents can maintain chat history in their respective tables

## Testing Individual Agents

### Test Amanda's Knowledge Base
```javascript
// In your application
const result = await amandaKnowledgeBaseTool.invoke({
  query: "How to manage anxiety?"
});
```

### Test Cody's Knowledge Base  
```javascript
const result = await codyKnowledgeBaseTool.invoke({
  query: "How to implement a REST API in Node.js?"
});
```

### Test Scott's Knowledge Base
```javascript
const result = await scoutKnowledgeBaseTool.invoke({
  query: "How to write a compelling resume?"
});
```

## Common Error Messages and Solutions

### "different vector dimensions 768 and 1536"
- **Cause**: RPC function expects wrong embedding dimensions
- **Solution**: Run the SQL fix script to update function signatures

### "Could not find the function public.match_[table_name]"
- **Cause**: RPC function doesn't exist
- **Solution**: Run the SQL fix script to create missing functions

### "JWT" or authentication errors
- **Cause**: Invalid service role key
- **Solution**: Verify `SUPABASE_SERVICE_ROLE_KEY` in your .env file

### "failed to fetch" errors
- **Cause**: Network connectivity or wrong Supabase URL
- **Solution**: Verify `NEXT_PUBLIC_SUPABASE_URL` and internet connection

### "memory required is 33 MB, maintenance_work_mem is 32 MB"
- **Cause**: Supabase memory limitation when creating vector indexes
- **Solution**: Use `fix-supabase-minimal.sql` instead of the full version
- **Note**: Your queries will work without indexes, just potentially slower

## Monitoring and Maintenance

### Regular Health Checks
Run the verification script periodically:
```bash
node verify-rag-agents.js
```

### Performance Optimization
- Ensure vector indexes are created (included in SQL fix)
- Monitor query performance in Supabase dashboard
- Consider adjusting `match_threshold` values based on your data quality

### Data Management
- Regularly update your knowledge base documents
- Monitor embedding quality and consistency
- Keep chat history tables from growing too large

## Next Steps After Fix

1. ✅ Verify all agents can connect to their databases
2. ✅ Test knowledge base searches with real queries
3. ✅ Confirm chat memory is working in n8n workflows
4. ✅ Monitor agent performance and response quality
5. ✅ Consider adding more documents to knowledge bases if needed

## Support

If you continue to experience issues after following this guide:
1. Check the Supabase logs in your dashboard
2. Verify your n8n agent configurations
3. Test individual components (embeddings, RPC functions, tables) separately
4. Ensure your Ollama service is running and accessible
