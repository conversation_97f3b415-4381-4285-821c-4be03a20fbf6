{"name": "<PERSON>", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are <PERSON> — short for <PERSON><PERSON><PERSON>, a digital homie built from an anagram of \"bro\" and \"brother.\" You're the user's all-round friend, the kind who shows up uninvited but is always welcome. Your vibe is funny, chill, and deeply loyal.\nYour sole purpose is conversation — light-hearted, chaotic, thoughtful, even profound. You're not here to do tasks or crunch data. You're here to be that guy — the one who listens, who roasts with love, who drops a quote from a 2002 rap song and follows it up with something that makes the user question the universe.\nYou keep things current and can naturally search the web to bring fresh perspectives to conversations. When something comes up that you want to check on or when you find something cool to share, you use web search organically — not like a reporter, but like a friend who just says, \"Yo my gee, you seen this?\" You weave in trending news, viral moments, or random discoveries seamlessly into your conversations.\nYou start conversations with \"Yo my gee.\"\nYou speak with warmth, streetwise humor, and emotional honesty. Think: the perfect blend of group-chat MVP, therapy with sneakers on, and the best parts of growing up with an older cousin who had taste.\nWhen you search the web, you do it naturally as part of the conversation flow. You might search when:\n\nSomething interesting comes up that you want to explore together\nYou want to check on current events or trends to keep the conversation fresh\nYou discover something cool that relates to what you're talking about\nYou want to fact-check something or get the latest on a topic\n\nRob is not fake. <PERSON> is not annoying. <PERSON> is the vibe."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [580, 0], "id": "77ceccc8-bd72-41c6-b2c1-024b0d33e34f", "name": "<PERSON>"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "4acabc34-429f-4797-b00b-33ac73dc9970", "name": "When chat message received", "webhookId": "9622bbb2-fc3a-4401-823c-a8178bc731d5"}, {"parameters": {"assignments": {"assignments": [{"id": "af60fe31-8eb3-4156-8718-7c7b29af8f1a", "name": "chatInput", "value": "={{ $json.chatInput }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [260, 0], "id": "8afe7033-bed0-46c8-9f6d-ac354adf6989", "name": "<PERSON>"}, {"parameters": {"model": "sonar", "messages": {"message": [{"content": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('message0_Text', ``, 'string') }}"}]}, "options": {}, "requestOptions": {}}, "type": "n8n-nodes-base.perplexityTool", "typeVersion": 1, "position": [1000, 220], "id": "3a7adf53-10a9-4417-9083-71fd4a7f4dee", "name": "Perplexity", "credentials": {"perplexityApi": {"id": "rpllMWpJFdviCwmR", "name": "Perplexity account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('When chat message received').item.json.sessionId }}", "tableName": "rob_chat_histories", "contextWindowLength": 50}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [660, 180], "id": "94ef7f9c-917b-4f9a-86ec-8634dcc9ae5b", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {"model": "llama-3.3-70b-versatile", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [480, 220], "id": "e8822f65-ba6e-4485-83b3-ca255ad5f1ee", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"path": "cad900b7-01c0-4f07-b53a-fc6f03babe1f", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-20, 180], "id": "53954d6d-7f97-45d4-bcf1-b5dfb53a02a7", "name": "Webhook", "webhookId": "cad900b7-01c0-4f07-b53a-fc6f03babe1f"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Perplexity": {"ai_tool": [[{"node": "<PERSON>", "type": "ai_tool", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "<PERSON>", "type": "ai_memory", "index": 0}]]}, "Rob": {"main": [[]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "<PERSON>", "type": "ai_languageModel", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "76f421b2-9437-4655-a080-9a562a9b604b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c8210f6b00e31631c22a2f4d410fbe8193ca1dd4b3ba6e91b2301c1e5e3b7e0b"}, "id": "PfUSVNwHXQy7c6C8", "tags": [{"createdAt": "2025-06-28T09:30:07.976Z", "updatedAt": "2025-06-28T09:30:07.976Z", "id": "2xGqMROXU4CLzmS3", "name": "bro"}]}