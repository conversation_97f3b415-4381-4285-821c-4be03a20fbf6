const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testOllamaEmbedding() {
  console.log('🔍 Testing Ollama Embedding Consistency...\n');

  // Test 1: Check if Ollama is running
  console.log('1️⃣ Checking Ollama service...');
  try {
    const response = await fetch('http://localhost:11434/api/tags');
    if (response.ok) {
      console.log('✅ Ollama is running');
      const data = await response.json();
      const models = data.models || [];
      const hasNomicEmbed = models.some(model => 
        model.name && model.name.includes('nomic-embed-text')
      );
      console.log(`✅ nomic-embed-text model: ${hasNomicEmbed ? 'Available' : 'Missing'}`);
      
      if (!hasNomicEmbed) {
        console.log('⚠️  Run: ollama pull nomic-embed-text:latest');
      }
    } else {
      throw new Error(`HTTP ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Ollama is not running or not accessible');
    console.log('   Please start Ollama and ensure it\'s running on http://localhost:11434');
    console.log('   Install from: https://ollama.ai');
    return;
  }

  // Test 2: Generate embedding with Ollama
  console.log('\n2️⃣ Testing Ollama embedding generation...');
  try {
    const response = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'nomic-embed-text:latest',
        prompt: 'test query for embedding',
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.embedding && Array.isArray(data.embedding)) {
      console.log(`✅ Ollama embedding generated: ${data.embedding.length} dimensions`);
      
      if (data.embedding.length === 768) {
        console.log('✅ Correct dimensions (768) - matches your Supabase database');
      } else {
        console.log(`⚠️  Unexpected dimensions: ${data.embedding.length} (expected 768)`);
      }
    } else {
      console.log('❌ Invalid embedding response format');
    }
  } catch (error) {
    console.log(`❌ Failed to generate embedding: ${error.message}`);
    return;
  }

  // Test 3: Test Supabase search with Ollama embedding
  console.log('\n3️⃣ Testing Supabase search with Ollama embedding...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Generate embedding for a test query
    const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'nomic-embed-text:latest',
        prompt: 'mental health anxiety coping strategies',
      }),
    });

    const embeddingData = await embeddingResponse.json();
    const queryEmbedding = embeddingData.embedding;

    // Test Amanda's knowledge base
    const { data, error } = await supabase.rpc('match_mental_health_documents', {
      query_embedding: queryEmbedding,
      match_threshold: 0.1, // Lower threshold for testing
      match_count: 3
    });

    if (error) {
      console.log(`❌ Supabase search failed: ${error.message}`);
    } else {
      console.log(`✅ Supabase search successful: ${data ? data.length : 0} results found`);
      
      if (data && data.length > 0) {
        console.log(`   📄 Sample result: "${data[0].content?.substring(0, 100)}..."`);
        console.log(`   🎯 Similarity score: ${data[0].similarity?.toFixed(3)}`);
      }
    }
  } catch (error) {
    console.log(`❌ Supabase search test failed: ${error.message}`);
  }

  // Test 4: Compare with Google AI embedding (if available)
  console.log('\n4️⃣ Comparing embedding approaches...');
  
  if (process.env.GEMINI_API_KEY) {
    console.log('✅ Google AI API key available');
    console.log('⚠️  Google AI embeddings have different dimensions than nomic-embed-text');
    console.log('   Your Next.js tools MUST use Ollama embeddings to match your database');
  } else {
    console.log('⚠️  No Google AI API key - this is fine if using Ollama only');
  }

  console.log('\n' + '='.repeat(60));
  console.log('📋 SUMMARY:');
  console.log('✅ Your Supabase database uses nomic-embed-text embeddings (768 dimensions)');
  console.log('✅ Your n8n agents use nomic-embed-text embeddings (768 dimensions)');
  console.log('⚠️  Your Next.js tools must also use nomic-embed-text embeddings');
  console.log('');
  console.log('🔧 SOLUTION:');
  console.log('1. Ensure Ollama is always running when using your RAG agents');
  console.log('2. Your Next.js tools now use the Ollama embedding utility');
  console.log('3. This ensures consistent 768-dimensional vectors across all systems');
  console.log('='.repeat(60));
}

testOllamaEmbedding().catch(console.error);
