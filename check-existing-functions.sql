-- Check what RPC functions currently exist in your Supabase database
-- Run this in your Supabase SQL Editor to see what's already there

-- List all match_* functions with their signatures
SELECT 
    proname as function_name,
    pg_get_function_arguments(oid) as arguments,
    pg_get_function_result(oid) as return_type
FROM pg_proc 
WHERE proname LIKE 'match_%' 
ORDER BY proname;

-- Alternative query if the above doesn't work
-- SELECT 
--     routine_name,
--     routine_type,
--     data_type
-- FROM information_schema.routines 
-- WHERE routine_name LIKE 'match_%'
-- ORDER BY routine_name;
