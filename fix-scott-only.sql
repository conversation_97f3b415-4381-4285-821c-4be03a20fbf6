-- Simple Fix: Only create <PERSON>'s missing function
-- This avoids conflicts with existing <PERSON> and <PERSON> functions
-- Run this if you just want to fix <PERSON>'s missing function

-- Drop <PERSON>'s function if it exists (try different signatures)
DROP FUNCTION IF EXISTS match_job_search_database(vector, float, int);
DROP FUNCTION IF EXISTS match_job_search_database(vector(768), float, int);
DROP FUNCTION IF EXISTS match_job_search_database(vector(1536), float, int);

-- Create <PERSON>'s function (Job Search) with 768-dimensional vectors
CREATE FUNCTION match_job_search_database(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    job_search_database.id,
    job_search_database.content,
    job_search_database.metadata,
    1 - (job_search_database.embedding <=> query_embedding) AS similarity
  FROM job_search_database
  WHERE 1 - (job_search_database.embedding <=> query_embedding) > match_threshold
  ORDER BY job_search_database.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION match_job_search_database TO anon, authenticated;
