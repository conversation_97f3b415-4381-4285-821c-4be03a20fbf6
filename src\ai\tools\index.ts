/**
 * @fileoverview This file serves as a central registry for all available Genkit tools.
 * It maps the user-facing tool names from tools.json to their corresponding
 * Genkit tool implementation.
 */
import { sendEmailTool } from './gmail';
import { amandaKnowledgeBaseTool, amandaAdviceGeneratorTool, codyKnowledgeBaseTool, scoutKnowledgeBaseTool, codyCodeGeneratorTool } from './supabase';
import { braveSearchTool, perplexitySearchTool, serpApiTool, serperSearchTool } from './web-search';

export const toolRegistry = {
  'Gmail': sendEmailTool,
  "Amanda's Knowledge Base": amandaKnowledgeBaseTool,
  "Amanda's Advice Generator": amandaAdviceGeneratorTool,
  "Cody's Knowledge Base": codyKnowledgeBaseTool,
  "Cody's Code Generator": codyCodeGeneratorTool,
  "Scout's Knowledge Base": scoutKnowledgeBaseTool,
  'Brave Search': braveSearchTool,
  'SerpApi Search': serpApiTool,
  'Serper Search': serperSearchTool,
  'Perplexity Search': perplexitySearchTool,
  // In the future, other tools can be added here.
  // e.g., 'Google Search': googleSearchTool,
};
