const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testCodyPydanticSearch() {
  console.log('🔍 Testing Cody\'s Pydantic AI Knowledge Base Search...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Test 1: Check what's actually in <PERSON>'s database
  console.log('1️⃣ Checking Cody\'s database content...');
  try {
    const { data, error } = await supabase
      .from('coding_database')
      .select('id, content, metadata')
      .ilike('content', '%pydantic%')
      .limit(5);

    if (error) {
      console.log(`❌ Error querying database: ${error.message}`);
    } else if (data && data.length > 0) {
      console.log(`✅ Found ${data.length} documents containing "pydantic"`);
      data.forEach((doc, index) => {
        console.log(`   📄 Document ${index + 1}:`);
        console.log(`      ID: ${doc.id}`);
        console.log(`      Content preview: "${doc.content.substring(0, 100)}..."`);
        if (doc.metadata) {
          console.log(`      Metadata: ${JSON.stringify(doc.metadata)}`);
        }
      });
    } else {
      console.log('❌ No documents found containing "pydantic"');
      
      // Check total document count
      const { data: totalData, error: totalError } = await supabase
        .from('coding_database')
        .select('id', { count: 'exact' });
      
      if (!totalError) {
        console.log(`   📊 Total documents in coding_database: ${totalData.length}`);
      }
    }
  } catch (error) {
    console.log(`❌ Database query failed: ${error.message}`);
  }

  // Test 2: Test vector search with Pydantic AI queries
  console.log('\n2️⃣ Testing vector search for Pydantic AI...');
  
  const pydanticQueries = [
    'pydantic ai agent',
    'pydantic ai framework',
    'create ai agent with pydantic',
    'pydantic ai movie script',
    'python ai agent pydantic'
  ];

  for (const query of pydanticQueries) {
    try {
      console.log(`\n🔍 Testing query: "${query}"`);
      
      // Generate embedding
      const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'nomic-embed-text:latest',
          prompt: query,
        }),
      });

      if (!embeddingResponse.ok) {
        console.log(`❌ Failed to generate embedding: ${embeddingResponse.status}`);
        continue;
      }

      const embeddingData = await embeddingResponse.json();
      const queryEmbedding = embeddingData.embedding;

      // Test with different thresholds
      const thresholds = [0.1, 0.3, 0.5, 0.7];
      
      for (const threshold of thresholds) {
        const { data, error } = await supabase.rpc('match_coding_database', {
          query_embedding: queryEmbedding,
          match_threshold: threshold,
          match_count: 3
        });

        if (error) {
          console.log(`   ❌ Threshold ${threshold}: ${error.message}`);
        } else {
          console.log(`   📊 Threshold ${threshold}: ${data ? data.length : 0} results`);
          
          if (data && data.length > 0) {
            data.forEach((result, index) => {
              console.log(`      ${index + 1}. Similarity: ${result.similarity?.toFixed(3)}`);
              console.log(`         Content: "${result.content?.substring(0, 80)}..."`);
            });
          }
        }
      }
    } catch (error) {
      console.log(`❌ Query "${query}" failed: ${error.message}`);
    }
  }

  // Test 3: Check if the tool is being called correctly
  console.log('\n3️⃣ Testing Cody\'s knowledge base tool simulation...');
  
  try {
    const toolQuery = "create an AI agent using pydantic AI that writes movie scripts";
    console.log(`Tool query: "${toolQuery}"`);
    
    // Generate embedding for the tool query
    const toolEmbeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'nomic-embed-text:latest',
        prompt: toolQuery,
      }),
    });

    const toolEmbeddingData = await toolEmbeddingResponse.json();
    const toolQueryEmbedding = toolEmbeddingData.embedding;

    // Use the same parameters as the actual tool
    const { data, error } = await supabase.rpc('match_coding_database', {
      query_embedding: toolQueryEmbedding,
      match_threshold: 0.75, // Same as in the tool
      match_count: 5 // Same as in the tool
    });

    if (error) {
      console.log(`❌ Tool simulation failed: ${error.message}`);
    } else if (!data || data.length === 0) {
      console.log('❌ Tool simulation: No results found with threshold 0.75');
      console.log('   This explains why Cody said he lacks the necessary information');
      
      // Try with lower threshold
      const { data: lowData, error: lowError } = await supabase.rpc('match_coding_database', {
        query_embedding: toolQueryEmbedding,
        match_threshold: 0.3,
        match_count: 5
      });
      
      if (!lowError && lowData && lowData.length > 0) {
        console.log(`✅ With threshold 0.3: Found ${lowData.length} results`);
        console.log('   💡 Suggestion: Lower the match_threshold in the tool configuration');
      }
    } else {
      console.log(`✅ Tool simulation: Found ${data.length} results`);
      
      // Format results like the actual tool would
      const formattedResults = data
        .map((item) => `- ${item.content}`)
        .join('\n');
      
      console.log('📋 Tool would return:');
      console.log(`Found the following information in the knowledge base:\n${formattedResults.substring(0, 500)}...`);
    }
  } catch (error) {
    console.log(`❌ Tool simulation error: ${error.message}`);
  }

  // Test 4: Check embedding quality
  console.log('\n4️⃣ Analyzing embedding quality...');
  
  try {
    // Get a sample of documents to check embedding quality
    const { data: sampleDocs, error: sampleError } = await supabase
      .from('coding_database')
      .select('content, embedding')
      .not('embedding', 'is', null)
      .limit(3);

    if (sampleError) {
      console.log(`❌ Failed to get sample documents: ${sampleError.message}`);
    } else if (sampleDocs && sampleDocs.length > 0) {
      console.log(`✅ Sample documents with embeddings: ${sampleDocs.length}`);
      
      sampleDocs.forEach((doc, index) => {
        const embedding = doc.embedding;
        const dimensions = Array.isArray(embedding) ? embedding.length : 'Unknown';
        console.log(`   📄 Document ${index + 1}:`);
        console.log(`      Embedding dimensions: ${dimensions}`);
        console.log(`      Content: "${doc.content.substring(0, 60)}..."`);
      });
    }
  } catch (error) {
    console.log(`❌ Embedding quality check failed: ${error.message}`);
  }

  console.log('\n' + '='.repeat(60));
  console.log('🔍 DIAGNOSIS SUMMARY:');
  console.log('If Cody says he lacks information about Pydantic AI, it could be:');
  console.log('1. 🎯 Match threshold too high (0.75) - try lowering to 0.3-0.5');
  console.log('2. 📄 Pydantic AI docs not in database or poorly embedded');
  console.log('3. 🔍 Query embedding not matching document embeddings well');
  console.log('4. 🛠️ Tool not being called or configured correctly');
  console.log('='.repeat(60));
}

testCodyPydanticSearch().catch(console.error);
