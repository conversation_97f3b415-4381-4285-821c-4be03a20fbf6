const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testAllRAGAgentsFixed() {
  console.log('🎉 Testing All RAG Agents with Fixed Thresholds...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Test scenarios that would have failed before
  const testScenarios = [
    {
      agent: '<PERSON>',
      name: 'Mental Health Assistant',
      rpc: 'match_mental_health_documents',
      queries: [
        'How to cope with anxiety and panic attacks?',
        'What are effective depression treatment options?',
        'Mindfulness techniques for stress relief',
        'Therapy approaches for trauma recovery'
      ]
    },
    {
      agent: '<PERSON>', 
      name: 'Coding Assistant',
      rpc: 'match_coding_database',
      queries: [
        'Create an AI agent using pydantic AI that writes movie scripts',
        'How to implement async/await in JavaScript?',
        'Python best practices for clean code',
        'React component design patterns'
      ]
    },
    {
      agent: '<PERSON>',
      name: 'Job Search Assistant', 
      rpc: 'match_job_search_database',
      queries: [
        'How to write an effective resume?',
        'Interview preparation strategies and tips',
        'Networking techniques for job searching',
        'Salary negotiation best practices'
      ]
    }
  ];

  let allTestsPassed = true;

  for (const scenario of testScenarios) {
    console.log(`🤖 Testing ${scenario.agent} (${scenario.name})...`);
    
    let agentPassed = true;
    
    for (const query of scenario.queries) {
      console.log(`\n   🔍 Query: "${query}"`);
      
      try {
        // Generate embedding using Ollama (same as the tools)
        const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'nomic-embed-text:latest',
            prompt: query,
          }),
        });

        if (!embeddingResponse.ok) {
          console.log(`      ❌ Failed to generate embedding`);
          agentPassed = false;
          continue;
        }

        const embeddingData = await embeddingResponse.json();
        const queryEmbedding = embeddingData.embedding;

        // Test with the new threshold (0.3)
        const { data, error } = await supabase.rpc(scenario.rpc, {
          query_embedding: queryEmbedding,
          match_threshold: 0.3,
          match_count: 5
        });

        if (error) {
          console.log(`      ❌ RPC Error: ${error.message}`);
          agentPassed = false;
        } else if (!data || data.length === 0) {
          console.log(`      ⚠️  No results found`);
          agentPassed = false;
        } else {
          console.log(`      ✅ Found ${data.length} results`);
          console.log(`      🎯 Best match (${data[0].similarity?.toFixed(3)}): "${data[0].content?.substring(0, 80)}..."`);
          
          // Simulate the tool response format
          const formattedResults = data
            .map((item) => `- ${item.content}`)
            .join('\n');
          
          console.log(`      📋 Tool response preview:`);
          console.log(`         "Found the following information in the knowledge base:`);
          console.log(`         ${formattedResults.substring(0, 150)}..."`);
        }

      } catch (error) {
        console.log(`      ❌ Test failed: ${error.message}`);
        agentPassed = false;
      }
    }

    if (agentPassed) {
      console.log(`\n   ✅ ${scenario.agent} is working correctly!`);
    } else {
      console.log(`\n   ❌ ${scenario.agent} has issues`);
      allTestsPassed = false;
    }
    
    console.log(''); // Empty line between agents
  }

  // Test the specific Pydantic AI issue that was reported
  console.log('🎯 Special Test: Cody\'s Pydantic AI Issue...');
  
  const pydanticQuery = "create an AI agent using pydantic AI that writes movie scripts based on user prompts";
  console.log(`Query: "${pydanticQuery}"`);
  
  try {
    const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'nomic-embed-text:latest',
        prompt: pydanticQuery,
      }),
    });

    const embeddingData = await embeddingResponse.json();
    const queryEmbedding = embeddingData.embedding;

    // Test with old threshold (0.75) vs new threshold (0.3)
    const oldResult = await supabase.rpc('match_coding_database', {
      query_embedding: queryEmbedding,
      match_threshold: 0.75,
      match_count: 5
    });

    const newResult = await supabase.rpc('match_coding_database', {
      query_embedding: queryEmbedding,
      match_threshold: 0.3,
      match_count: 5
    });

    console.log(`📊 Old threshold (0.75): ${oldResult.data ? oldResult.data.length : 0} results`);
    console.log(`📊 New threshold (0.3): ${newResult.data ? newResult.data.length : 0} results`);
    
    if (newResult.data && newResult.data.length > 0) {
      console.log(`✅ FIXED! Cody can now find Pydantic AI information`);
      console.log(`   Best result: "${newResult.data[0].content?.substring(0, 100)}..."`);
    } else {
      console.log(`❌ Still not working`);
      allTestsPassed = false;
    }

  } catch (error) {
    console.log(`❌ Pydantic AI test failed: ${error.message}`);
    allTestsPassed = false;
  }

  // Final summary
  console.log('\n' + '='.repeat(60));
  if (allTestsPassed) {
    console.log('🎉 SUCCESS! All RAG Agents Are Now Working Properly!');
    console.log('');
    console.log('✅ Amanda: Can find mental health information');
    console.log('✅ Cody: Can find programming documentation (including Pydantic AI)');
    console.log('✅ Scott: Can find job search advice');
    console.log('');
    console.log('🔧 CHANGES MADE:');
    console.log('• Lowered match_threshold from 0.75 to 0.3 for all agents');
    console.log('• Fixed embedding consistency (all use nomic-embed-text)');
    console.log('• All RPC functions are working correctly');
    console.log('');
    console.log('🚀 NEXT STEPS:');
    console.log('1. Restart your Next.js server if not already done');
    console.log('2. Test your RAG agents in the web interface');
    console.log('3. They should now provide helpful, relevant responses!');
  } else {
    console.log('⚠️  Some issues remain. Please check the errors above.');
  }
  console.log('='.repeat(60));
}

testAllRAGAgentsFixed().catch(console.error);
