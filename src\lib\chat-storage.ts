// Chat storage utilities for Supabase persistence
'use server';

import { createClient } from '@supabase/supabase-js';

export interface ChatMessage {
  id?: number;
  session_id: string;
  agent_id: string;
  message_type: 'human' | 'ai';
  content: string;
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface ChatSession {
  session_id: string;
  agent_id: string;
  agent_name: string;
  last_message_at: string;
  message_count: number;
}

// Get Supabase client
function getSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Supabase configuration missing. Please check your environment variables.');
  }

  return createClient(supabaseUrl, supabaseServiceKey);
}

// Get the correct table name for an agent
function getAgentChatTable(agentId: string): string {
  // Map agent IDs to their chat history tables
  const agentTableMap: Record<string, string> = {
    'NeK3H8lDKNXHFHVV': 'amanda_chat_histories',    // Amanda
    'Mq57tleDBanDxipt': 'cody_chat_histories',      // Cody  
    '4jn4JiOzQZuclHRD': 'scout_chat_histories',     // Scout
    'PfUSVNwHXQy7c6C8': 'rob_chat_histories',       // Rob
    'x3M1ATR5WY16vkPd': 'n8n_chat_histories',       // Li
    'k1C6YbeQ5QW2vlSp': 'n8n_chat_histories',       // Orion
  };

  return agentTableMap[agentId] || 'n8n_chat_histories'; // Default fallback
}

/**
 * Save a chat message to Supabase
 */
export async function saveChatMessage(
  sessionId: string,
  agentId: string,
  messageType: 'human' | 'ai',
  content: string,
  metadata?: Record<string, any>
): Promise<ChatMessage | null> {
  try {
    const supabase = getSupabaseClient();
    const tableName = getAgentChatTable(agentId);

    const messageData = {
      session_id: sessionId,
      message_type: messageType,
      content: content,
      metadata: metadata || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // For n8n_chat_histories table, add agent_name field
    if (tableName === 'n8n_chat_histories') {
      const agentNames: Record<string, string> = {
        'x3M1ATR5WY16vkPd': 'Li',
        'k1C6YbeQ5QW2vlSp': 'Orion'
      };
      (messageData as any).agent_name = agentNames[agentId] || 'Unknown';
    }

    const { data, error } = await supabase
      .from(tableName)
      .insert(messageData)
      .select()
      .single();

    if (error) {
      console.error(`Error saving message to ${tableName}:`, error);
      return null;
    }

    return data as ChatMessage;
  } catch (error) {
    console.error('Error in saveChatMessage:', error);
    return null;
  }
}

/**
 * Load chat history for a session
 */
export async function loadChatHistory(
  sessionId: string,
  agentId: string,
  limit: number = 50
): Promise<ChatMessage[]> {
  try {
    const supabase = getSupabaseClient();
    const tableName = getAgentChatTable(agentId);

    let query = supabase
      .from(tableName)
      .select('*')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true })
      .limit(limit);

    // For n8n_chat_histories, also filter by agent_name
    if (tableName === 'n8n_chat_histories') {
      const agentNames: Record<string, string> = {
        'x3M1ATR5WY16vkPd': 'Li',
        'k1C6YbeQ5QW2vlSp': 'Orion'
      };
      const agentName = agentNames[agentId];
      if (agentName) {
        query = query.eq('agent_name', agentName);
      }
    }

    const { data, error } = await query;

    if (error) {
      console.error(`Error loading chat history from ${tableName}:`, error);
      return [];
    }

    return (data as ChatMessage[]) || [];
  } catch (error) {
    console.error('Error in loadChatHistory:', error);
    return [];
  }
}

/**
 * Get recent chat sessions for an agent
 */
export async function getRecentChatSessions(
  agentId: string,
  limit: number = 10
): Promise<ChatSession[]> {
  try {
    const supabase = getSupabaseClient();
    const tableName = getAgentChatTable(agentId);

    let query = supabase
      .from(tableName)
      .select('session_id, created_at')
      .order('created_at', { ascending: false });

    // For n8n_chat_histories, also filter by agent_name
    if (tableName === 'n8n_chat_histories') {
      const agentNames: Record<string, string> = {
        'x3M1ATR5WY16vkPd': 'Li',
        'k1C6YbeQ5QW2vlSp': 'Orion'
      };
      const agentName = agentNames[agentId];
      if (agentName) {
        query = query.eq('agent_name', agentName);
      }
    }

    const { data, error } = await query;

    if (error) {
      console.error(`Error getting recent sessions from ${tableName}:`, error);
      return [];
    }

    // Group by session_id and get session info
    const sessionMap = new Map<string, { lastMessageAt: string; count: number }>();
    
    data?.forEach((row: any) => {
      const sessionId = row.session_id;
      const createdAt = row.created_at;
      
      if (!sessionMap.has(sessionId)) {
        sessionMap.set(sessionId, { lastMessageAt: createdAt, count: 1 });
      } else {
        const session = sessionMap.get(sessionId)!;
        session.count++;
        if (createdAt > session.lastMessageAt) {
          session.lastMessageAt = createdAt;
        }
      }
    });

    // Convert to ChatSession array and sort by last message time
    const sessions: ChatSession[] = Array.from(sessionMap.entries())
      .map(([sessionId, info]) => ({
        session_id: sessionId,
        agent_id: agentId,
        agent_name: getAgentNameById(agentId),
        last_message_at: info.lastMessageAt,
        message_count: info.count
      }))
      .sort((a, b) => new Date(b.last_message_at).getTime() - new Date(a.last_message_at).getTime())
      .slice(0, limit);

    return sessions;
  } catch (error) {
    console.error('Error in getRecentChatSessions:', error);
    return [];
  }
}

/**
 * Delete a chat session
 */
export async function deleteChatSession(
  sessionId: string,
  agentId: string
): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();
    const tableName = getAgentChatTable(agentId);

    let query = supabase
      .from(tableName)
      .delete()
      .eq('session_id', sessionId);

    // For n8n_chat_histories, also filter by agent_name
    if (tableName === 'n8n_chat_histories') {
      const agentNames: Record<string, string> = {
        'x3M1ATR5WY16vkPd': 'Li',
        'k1C6YbeQ5QW2vlSp': 'Orion'
      };
      const agentName = agentNames[agentId];
      if (agentName) {
        query = query.eq('agent_name', agentName);
      }
    }

    const { error } = await query;

    if (error) {
      console.error(`Error deleting session from ${tableName}:`, error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteChatSession:', error);
    return false;
  }
}

/**
 * Get agent name by ID
 */
function getAgentNameById(agentId: string): string {
  const agentNames: Record<string, string> = {
    'NeK3H8lDKNXHFHVV': 'Amanda',
    'Mq57tleDBanDxipt': 'Cody',
    '4jn4JiOzQZuclHRD': 'Scout',
    'PfUSVNwHXQy7c6C8': 'Rob',
    'x3M1ATR5WY16vkPd': 'Li',
    'k1C6YbeQ5QW2vlSp': 'Orion'
  };
  
  return agentNames[agentId] || 'Unknown Agent';
}

/**
 * Check if chat storage is properly configured
 */
export async function testChatStorage(): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = getSupabaseClient();
    
    // Test connection by checking one of the tables
    const { data, error } = await supabase
      .from('amanda_chat_histories')
      .select('id')
      .limit(1);

    if (error) {
      return {
        success: false,
        message: `Database connection failed: ${error.message}`
      };
    }

    return {
      success: true,
      message: 'Chat storage is properly configured and connected'
    };
  } catch (error) {
    return {
      success: false,
      message: `Configuration error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
