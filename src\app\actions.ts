
'use server';

import { agent<PERSON>low, type AgentFlowInput } from "@/ai/flows/agent-flow";
import { z } from "zod";
import { speechToText } from '@/ai/flows/stt-flow';
import { textToSpeech } from '@/ai/flows/tts-flow';
import type { Agent, Tool } from '@/lib/types';
import fs from 'fs/promises';
import path from 'path';
import { revalidatePath } from 'next/cache';
import { randomUUID } from "crypto";
import { sendEmailTool } from "@/ai/tools/gmail";
import { saveChatMessage, loadChatHistory } from '@/lib/chat-storage';


interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export type ChatActionState = {
  messages: ChatMessage[];
  error?: string;
};

const AgentChatActionInputSchema = z.object({
  prompt: z.string().min(1, { message: "Message cannot be empty." }),
  agentId: z.string(),
  sessionId: z.string(),
});

const LoadChatHistoryInputSchema = z.object({
  agentId: z.string(),
  sessionId: z.string(),
});

export async function agentChatAction(
  prevState: ChatActionState,
  formData: FormData
): Promise<ChatActionState> {
  const validatedFields = AgentChatActionInputSchema.safeParse({
    prompt: formData.get('prompt'),
    agentId: formData.get('agentId'),
    sessionId: formData.get('sessionId'),
  });

  if (!validatedFields.success) {
    return {
      ...prevState,
      error: validatedFields.error.flatten().fieldErrors.prompt?.[0],
    };
  }

  const { prompt, agentId, sessionId } = validatedFields.data;

  const userMessage: ChatMessage = { role: 'user', content: prompt };
  const newMessages = [...prevState.messages, userMessage];

  try {
    // Save user message to Supabase
    await saveChatMessage(sessionId, agentId, 'human', prompt);

    const chatInput: AgentFlowInput = { agentId, prompt, sessionId };
    const result = await agentFlow(chatInput);

    // Save assistant response to Supabase
    await saveChatMessage(sessionId, agentId, 'ai', result.response);

    const assistantMessage: ChatMessage = { role: 'assistant', content: result.response };
    return {
      messages: [...newMessages, assistantMessage],
    };

  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : "An unknown error occurred.";
    return {
      messages: newMessages,
      error: errorMessage,
    };
  }
}

export async function loadChatHistoryAction(
  agentId: string,
  sessionId: string
): Promise<ChatActionState> {
  try {
    const validatedFields = LoadChatHistoryInputSchema.safeParse({
      agentId,
      sessionId,
    });

    if (!validatedFields.success) {
      return {
        messages: [{ role: 'assistant', content: `Hello! How can I assist you today?` }],
        error: 'Invalid parameters for loading chat history',
      };
    }

    // Load chat history from Supabase
    const chatHistory = await loadChatHistory(sessionId, agentId);

    // Convert to ChatMessage format
    const messages: ChatMessage[] = chatHistory.map(msg => ({
      role: msg.message_type === 'human' ? 'user' : 'assistant',
      content: msg.content
    }));

    // If no history exists, start with a greeting
    if (messages.length === 0) {
      return {
        messages: [{ role: 'assistant', content: `Hello! How can I assist you today?` }],
      };
    }

    return {
      messages,
    };

  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : "An unknown error occurred while loading chat history.";
    console.error("Load Chat History Error:", errorMessage);
    return {
      messages: [{ role: 'assistant', content: `Hello! How can I assist you today?` }],
      error: errorMessage,
    };
  }
}

const TranscribeAudioInputSchema = z.object({
  audioDataUri: z.string(),
});

export async function transcribeAudioAction(
  formData: FormData
): Promise<{ transcription?: string; error?: string; }> {
  const validatedFields = TranscribeAudioInputSchema.safeParse({
    audioDataUri: formData.get('audioDataUri'),
  });

  if (!validatedFields.success) {
    return { error: 'Invalid audio data.' };
  }

  try {
    const { transcription } = await speechToText(validatedFields.data);
    return { transcription };
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : "An unknown error occurred during transcription.";
    console.error("STT Action Error:", errorMessage);
    return { error: errorMessage };
  }
}

const SynthesizeSpeechInputSchema = z.object({
  text: z.string(),
});

export async function synthesizeSpeechAction(
  formData: FormData
): Promise<{ audioDataUri?: string; error?: string }> {
  const validatedFields = SynthesizeSpeechInputSchema.safeParse({
    text: formData.get('text'),
  });

  if (!validatedFields.success) {
    return { error: 'Invalid text for speech synthesis.' };
  }

  try {
    const { audioDataUri } = await textToSpeech(validatedFields.data);
    return { audioDataUri };
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : "An unknown error occurred during speech synthesis.";
    console.error("TTS Action Error:", errorMessage);
    return { error: errorMessage };
  }
}

const settingsFilePath = path.join(process.cwd(), 'src', 'lib', 'agent-settings.json');
const toolsFilePath = path.join(process.cwd(), 'src', 'lib', 'tools.json');

export async function saveAgentSettingsAction(agents: Agent[]): Promise<{ success: boolean; error?: string }> {
  try {
    const data = JSON.stringify(agents, null, 2);
    await fs.writeFile(settingsFilePath, data, 'utf-8');

    revalidatePath('/settings');
    revalidatePath('/agents');
    revalidatePath('/');
    revalidatePath('/tools');

    return { success: true };
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : "An unknown error occurred.";
    console.error("Failed to save agent settings:", errorMessage);
    return { success: false, error: errorMessage };
  }
}


const ToolFormSchema = z.object({
  name: z.string().min(3, { message: "Tool name must be at least 3 characters." }),
  description: z.string().min(10, { message: "Description must be at least 10 characters." }),
});

export async function saveToolAction(
  prevState: { error?: string },
  formData: FormData
): Promise<{ success?: boolean; error?: string; }> {
  const validatedFields = ToolFormSchema.safeParse({
    name: formData.get('name'),
    description: formData.get('description'),
  });

  if (!validatedFields.success) {
    const errors = validatedFields.error.flatten().fieldErrors;
    return {
      error: errors.name?.[0] || errors.description?.[0] || 'Invalid input.',
    };
  }
  
  const { name, description } = validatedFields.data;

  const newTool: Tool = {
    id: randomUUID(),
    name,
    description,
    icon: 'Wrench',
    enabled: true,
  };

  try {
    let tools: Tool[] = [];
    try {
      const fileContent = await fs.readFile(toolsFilePath, 'utf-8');
      if (fileContent) {
        const parsedData = JSON.parse(fileContent);
        if (Array.isArray(parsedData)) {
            tools = parsedData;
        }
      }
    } catch (e: any) {
      // If the file doesn't exist (ENOENT) or is not valid JSON, we'll start with an empty array.
      if (e.code !== 'ENOENT') {
        console.error("Error reading or parsing tools.json:", e.message);
      }
    }
    
    tools.push(newTool);

    await fs.writeFile(toolsFilePath, JSON.stringify(tools, null, 2), 'utf-8');
    
    revalidatePath('/tools');

    return { success: true };

  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : "An unknown error occurred.";
    console.error("Failed to save tool:", errorMessage);
    return { error: errorMessage };
  }
}

const SendEmailSchema = z.object({
  to: z.string().email(),
  subject: z.string(),
  body: z.string(),
});

export type SendEmailActionState = {
  message?: string;
  error?: string;
}

export async function sendEmailAction(
  prevState: SendEmailActionState,
  formData: FormData
): Promise<SendEmailActionState> {
  const validatedFields = SendEmailSchema.safeParse({
    to: formData.get('to'),
    subject: formData.get('subject'),
    body: formData.get('body'),
  });

  if (!validatedFields.success) {
    console.error("Email validation failed:", validatedFields.error.flatten());
    return { error: 'Invalid email data provided. Please try again.' };
  }

  try {
    const result = await sendEmailTool(validatedFields.data);
    return { message: result };
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred while sending the email.';
    console.error("Send Email Action Error:", errorMessage);
    return { error: errorMessage };
  }
}

    