const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function checkTableSimple() {
  console.log('🔍 Checking Chat Table Contents...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Try to select from amanda_chat_histories to see what columns exist
  console.log('📋 Testing amanda_chat_histories...');
  try {
    const { data, error } = await supabase
      .from('amanda_chat_histories')
      .select('*')
      .limit(1);

    if (error) {
      console.log(`   ❌ Error: ${error.message}`);
    } else {
      console.log(`   ✅ Table accessible, sample data:`, data);
    }
  } catch (error) {
    console.log(`   ❌ Query failed: ${error.message}`);
  }

  // Try to select from n8n_chat_histories
  console.log('\n📋 Testing n8n_chat_histories...');
  try {
    const { data, error } = await supabase
      .from('n8n_chat_histories')
      .select('*')
      .limit(1);

    if (error) {
      console.log(`   ❌ Error: ${error.message}`);
    } else {
      console.log(`   ✅ Table accessible, sample data:`, data);
    }
  } catch (error) {
    console.log(`   ❌ Query failed: ${error.message}`);
  }

  console.log('\n🔧 NEXT STEPS:');
  console.log('1. Run the setup-chat-memory-tables.sql script in your Supabase SQL Editor');
  console.log('2. This will create the tables with the correct schema');
  console.log('3. Then test the chat storage again');
}

checkTableSimple().catch(console.error);
