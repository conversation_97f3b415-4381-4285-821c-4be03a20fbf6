# Complete RAG Agents Setup Guide

## 🎯 **What Your RAG Agents Need to Access Their Databases**

Your RAG agents have **two different implementations** that need to work together:

### **1. n8n Agents (<PERSON>, <PERSON>, <PERSON>)**
- ✅ Use Ollama `nomic-embed-text:latest` (768 dimensions)
- ✅ Connect directly to Supabase via n8n nodes
- ✅ Have proper credentials configured

### **2. Next.js/Genkit Tools (Web Interface)**
- ❌ Were using Google AI embeddings (different dimensions)
- ❌ Causing vector dimension mismatches
- ✅ **Now fixed** to use consistent Ollama embeddings

## 🔧 **Required Components**

### **1. Supabase Database Setup**
- ✅ Tables: `mental_health_documents`, `coding_database`, `job_search_database`
- ✅ Chat memory tables: `amanda_chat_histories`, `cody_chat_histories`, `scout_chat_histories`
- ❌ **Missing**: `match_job_search_database` RPC function
- ⚠️ **Needs verification**: All RPC functions use 768-dimensional vectors

### **2. Ollama Service**
- ❌ **Required**: Ollama running on `http://localhost:11434`
- ❌ **Required**: `nomic-embed-text:latest` model installed
- ❌ **Required**: Accessible from both n8n and Next.js

### **3. n8n Service**
- ❌ **Required**: n8n running on `http://localhost:5678`
- ✅ Agent workflows configured with proper credentials
- ✅ Webhook URLs configured in environment variables

### **4. Environment Variables**
- ✅ Supabase credentials configured
- ✅ API keys for various services
- ✅ n8n webhook URLs

## 🚀 **Setup Steps**

### **Step 1: Fix Supabase Database**
```bash
# Run this in your Supabase SQL Editor
# Use the fix-function-conflicts.sql script
```

### **Step 2: Install and Start Ollama**
```bash
# Install Ollama (if not already installed)
# Download from: https://ollama.ai

# Pull the embedding model
ollama pull nomic-embed-text:latest

# Verify Ollama is running
curl http://localhost:11434/api/tags
```

### **Step 3: Start n8n Service**
```bash
# Make sure n8n is running on port 5678
# Your agents should be accessible via webhooks
```

### **Step 4: Test the Setup**
```bash
# Run the verification script
node verify-rag-agents.js
```

## 🔍 **How Each Agent Accesses Their Database**

### **Amanda (Mental Health)**
1. **n8n Workflow**: Receives webhook → Ollama embedding → Supabase vector search
2. **Next.js Tool**: `amandaKnowledgeBaseTool` → Ollama embedding → `match_mental_health_documents`

### **Cody (Coding)**
1. **n8n Workflow**: Receives webhook → Ollama embedding → Supabase vector search
2. **Next.js Tool**: `codyKnowledgeBaseTool` → Ollama embedding → `match_coding_database`

### **Scott (Job Search)**
1. **n8n Workflow**: Receives webhook → Ollama embedding → Supabase vector search
2. **Next.js Tool**: `scoutKnowledgeBaseTool` → Ollama embedding → `match_job_search_database`

## ⚠️ **Current Issues & Solutions**

### **Issue 1: Scott's Missing RPC Function**
- **Problem**: `match_job_search_database` doesn't exist
- **Solution**: Run `fix-function-conflicts.sql`

### **Issue 2: Embedding Model Inconsistency**
- **Problem**: Next.js was using Google AI embeddings, n8n uses Ollama
- **Solution**: Updated Next.js to use Ollama embeddings consistently

### **Issue 3: Ollama Service Dependency**
- **Problem**: Both systems need Ollama running
- **Solution**: Ensure Ollama is always running, with fallback to Google AI

## 🧪 **Testing Each Component**

### **Test Ollama Embeddings**
```bash
curl -X POST http://localhost:11434/api/embeddings \
  -H "Content-Type: application/json" \
  -d '{"model": "nomic-embed-text:latest", "prompt": "test query"}'
```

### **Test Supabase RPC Functions**
```javascript
// Test in browser console or Node.js
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(url, key);

const testEmbedding = new Array(768).fill(0.1);
const result = await supabase.rpc('match_mental_health_documents', {
  query_embedding: testEmbedding,
  match_threshold: 0.1,
  match_count: 1
});
```

### **Test n8n Webhooks**
```bash
curl -X POST http://localhost:5678/webhook/[webhook-id] \
  -H "Content-Type: application/json" \
  -d '{"chatInput": "test message"}'
```

## 🎯 **Success Criteria**

When everything is working correctly:

1. ✅ All three RPC functions exist and work with 768-dimensional vectors
2. ✅ Ollama is running with `nomic-embed-text:latest` model
3. ✅ n8n agents respond to webhook calls
4. ✅ Next.js tools can search knowledge bases
5. ✅ Both systems use consistent embedding dimensions
6. ✅ Chat memory is preserved across conversations

## 🔄 **Architecture Flow**

```
User Query → Next.js Interface → Genkit Tool → Ollama Embedding → Supabase RPC → Results
                                     ↓
User Query → n8n Webhook → n8n Agent → Ollama Embedding → Supabase Vector Store → Response
```

Both paths now use the same embedding model and database functions for consistency!
