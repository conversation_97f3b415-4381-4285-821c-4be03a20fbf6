-- Step-by-Step Fix for Supabase RAG Agent Database Issues
-- Run each section separately to handle function conflicts safely

-- STEP 1: First, let's see what functions exist (run this first)
-- Copy and paste this query to see current functions:

SELECT 
    proname as function_name,
    oidvectortypes(proargtypes) as argument_types
FROM pg_proc 
WHERE proname IN ('match_mental_health_documents', 'match_coding_database', 'match_job_search_database')
ORDER BY proname;

-- STEP 2: Drop functions using dynamic SQL (run this second)
-- This will handle multiple overloads safely

DO $$
DECLARE
    func_record RECORD;
BEGIN
    -- Drop all versions of match_mental_health_documents
    FOR func_record IN 
        SELECT oid::regprocedure as func_signature
        FROM pg_proc 
        WHERE proname = 'match_mental_health_documents'
    LOOP
        EXECUTE 'DROP FUNCTION IF EXISTS ' || func_record.func_signature;
    END LOOP;
    
    -- Drop all versions of match_coding_database
    FOR func_record IN 
        SELECT oid::regprocedure as func_signature
        FROM pg_proc 
        WHERE proname = 'match_coding_database'
    LOOP
        EXECUTE 'DROP FUNCTION IF EXISTS ' || func_record.func_signature;
    END LOOP;
    
    -- Drop all versions of match_job_search_database
    FOR func_record IN 
        SELECT oid::regprocedure as func_signature
        FROM pg_proc 
        WHERE proname = 'match_job_search_database'
    LOOP
        EXECUTE 'DROP FUNCTION IF EXISTS ' || func_record.func_signature;
    END LOOP;
END $$;

-- STEP 3: Create new functions (run this third)

-- Amanda's function (Mental Health)
CREATE FUNCTION match_mental_health_documents(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    mental_health_documents.id,
    mental_health_documents.content,
    mental_health_documents.metadata,
    1 - (mental_health_documents.embedding <=> query_embedding) AS similarity
  FROM mental_health_documents
  WHERE 1 - (mental_health_documents.embedding <=> query_embedding) > match_threshold
  ORDER BY mental_health_documents.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Cody's function (Coding)
CREATE FUNCTION match_coding_database(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    coding_database.id,
    coding_database.content,
    coding_database.metadata,
    1 - (coding_database.embedding <=> query_embedding) AS similarity
  FROM coding_database
  WHERE 1 - (coding_database.embedding <=> query_embedding) > match_threshold
  ORDER BY coding_database.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Scott's function (Job Search)
CREATE FUNCTION match_job_search_database(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    job_search_database.id,
    job_search_database.content,
    job_search_database.metadata,
    1 - (job_search_database.embedding <=> query_embedding) AS similarity
  FROM job_search_database
  WHERE 1 - (job_search_database.embedding <=> query_embedding) > match_threshold
  ORDER BY job_search_database.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- STEP 4: Grant permissions (run this last)
GRANT EXECUTE ON FUNCTION match_mental_health_documents TO anon, authenticated;
GRANT EXECUTE ON FUNCTION match_coding_database TO anon, authenticated;
GRANT EXECUTE ON FUNCTION match_job_search_database TO anon, authenticated;
