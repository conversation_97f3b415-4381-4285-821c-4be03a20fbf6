'use client';

import type { Agent, Activity } from '@/lib/types';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { ScrollArea } from './ui/scroll-area';
import { Badge } from './ui/badge';
import { AnimatePresence, motion } from 'framer-motion';

const stepColors: Record<string, string> = {
  Reasoning: 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300',
  'Tool Use': 'bg-teal-100 text-teal-800 dark:bg-teal-900/50 dark:text-teal-300',
  Drafting: 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300',
  Analysis: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300',
  Coding: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',
  Listening: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
};


export function AgentActivityFeed({ agent }: { agent: Agent }) {
  const [activities, setActivities] = useState<Activity[]>(agent.activity);

  // In a real application, you would subscribe to a real-time feed of agent activities.
  // For now, we are just displaying the initial state.

  return (
    <Card>
      <CardHeader>
        <CardTitle>Activity Feed</CardTitle>
        <CardDescription>A real-time stream of the agent's actions.</CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-6">
            <AnimatePresence initial={false}>
              {activities.map((activity, index) => (
                <motion.div
                  key={activity.id}
                  layout
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9, transition: { duration: 0.2 } }}
                  transition={{ type: 'spring', stiffness: 300, damping: 30, duration: 0.4 }}
                  className="relative flex"
                >
                  <div className="absolute left-0 top-0 h-full w-0.5 bg-border -translate-x-1/2 ml-[9px]"></div>
                  <div className="flex h-5 w-5 items-center justify-center rounded-full bg-primary/20 ring-4 ring-background z-10 shrink-0">
                    <div className="h-2 w-2 rounded-full bg-primary"></div>
                  </div>
                  <div className="ml-4 flex-grow">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className={`border-0 ${stepColors[activity.step] || 'bg-gray-100 text-gray-800'}`}>{activity.step}</Badge>
                      <span className="text-xs text-muted-foreground">{activity.timestamp}</span>
                    </div>
                    <p className="text-sm font-code text-foreground">{activity.details}</p>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
