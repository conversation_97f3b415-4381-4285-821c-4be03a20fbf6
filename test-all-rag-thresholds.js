const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testAllRAGThresholds() {
  console.log('🔍 Testing Optimal Thresholds for All RAG Agents...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Test queries for each agent
  const agents = [
    {
      name: '<PERSON> (Mental Health)',
      table: 'mental_health_documents',
      rpc: 'match_mental_health_documents',
      testQueries: [
        'anxiety coping strategies',
        'depression treatment options',
        'stress management techniques',
        'therapy approaches for trauma',
        'mindfulness meditation benefits'
      ]
    },
    {
      name: '<PERSON> (Coding)',
      table: 'coding_database', 
      rpc: 'match_coding_database',
      testQueries: [
        'pydantic ai framework',
        'javascript async await',
        'python best practices',
        'react component patterns',
        'database optimization techniques'
      ]
    },
    {
      name: '<PERSON> (Job Search)',
      table: 'job_search_database',
      rpc: 'match_job_search_database', 
      testQueries: [
        'resume writing tips',
        'interview preparation strategies',
        'job search networking',
        'salary negotiation advice',
        'career change guidance'
      ]
    }
  ];

  const thresholds = [0.1, 0.3, 0.5, 0.7, 0.75, 0.8];

  for (const agent of agents) {
    console.log(`🤖 Testing ${agent.name}...`);
    console.log(`   📊 Database: ${agent.table}`);
    
    // Check database content first
    try {
      const { data: totalData, error: totalError } = await supabase
        .from(agent.table)
        .select('id', { count: 'exact' });
      
      if (!totalError) {
        console.log(`   📄 Total documents: ${totalData.length}`);
      }
    } catch (error) {
      console.log(`   ❌ Could not count documents: ${error.message}`);
    }

    // Test each query with different thresholds
    for (const query of agent.testQueries) {
      console.log(`\n   🔍 Query: "${query}"`);
      
      try {
        // Generate embedding
        const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'nomic-embed-text:latest',
            prompt: query,
          }),
        });

        if (!embeddingResponse.ok) {
          console.log(`      ❌ Failed to generate embedding`);
          continue;
        }

        const embeddingData = await embeddingResponse.json();
        const queryEmbedding = embeddingData.embedding;

        // Test different thresholds
        const results = [];
        for (const threshold of thresholds) {
          const { data, error } = await supabase.rpc(agent.rpc, {
            query_embedding: queryEmbedding,
            match_threshold: threshold,
            match_count: 5
          });

          if (error) {
            results.push({ threshold, count: 'ERROR', error: error.message });
          } else {
            const count = data ? data.length : 0;
            const bestSimilarity = data && data.length > 0 ? data[0].similarity : null;
            results.push({ threshold, count, bestSimilarity });
          }
        }

        // Display results
        console.log(`      📊 Results by threshold:`);
        results.forEach(result => {
          if (result.error) {
            console.log(`         ${result.threshold}: ERROR - ${result.error}`);
          } else {
            const similarity = result.bestSimilarity ? ` (best: ${result.bestSimilarity.toFixed(3)})` : '';
            console.log(`         ${result.threshold}: ${result.count} results${similarity}`);
          }
        });

        // Find optimal threshold (first one that gives good results)
        const optimalResult = results.find(r => r.count > 0 && r.count !== 'ERROR');
        if (optimalResult) {
          console.log(`      ✅ Suggested threshold: ${optimalResult.threshold} (${optimalResult.count} results)`);
        } else {
          console.log(`      ⚠️  No results found at any threshold`);
        }

      } catch (error) {
        console.log(`      ❌ Query failed: ${error.message}`);
      }
    }

    console.log(''); // Empty line between agents
  }

  // Summary and recommendations
  console.log('=' * 60);
  console.log('📋 THRESHOLD RECOMMENDATIONS:');
  console.log('');
  console.log('Based on the tests above, here are the recommended thresholds:');
  console.log('');
  console.log('🧠 Amanda (Mental Health):');
  console.log('   Current: 0.75 → Recommended: 0.3-0.5');
  console.log('   Reason: Mental health queries often need broader semantic matching');
  console.log('');
  console.log('💻 Cody (Coding):');
  console.log('   Current: 0.75 → Recommended: 0.3 (already fixed)');
  console.log('   Reason: Technical documentation benefits from lower thresholds');
  console.log('');
  console.log('💼 Scott (Job Search):');
  console.log('   Current: 0.75 → Recommended: 0.3-0.5');
  console.log('   Reason: Career advice queries need flexible matching');
  console.log('');
  console.log('🎯 GENERAL RULE:');
  console.log('   0.3-0.5: Good balance of relevance and recall');
  console.log('   0.7+: Very strict, may miss relevant content');
  console.log('   0.1-0.2: Very loose, may include irrelevant content');
  console.log('=' * 60);
}

testAllRAGThresholds().catch(console.error);
