const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testAmandaEnhancements() {
  console.log('🧠 Testing Amanda\'s Enhanced Therapeutic Capabilities...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Test 1: Check chat memory tables
  console.log('1️⃣ Testing chat memory tables...');
  
  const chatTables = [
    { table: 'amanda_chat_histories', agent: '<PERSON>' },
    { table: 'cody_chat_histories', agent: '<PERSON>' },
    { table: 'scout_chat_histories', agent: '<PERSON>' },
    { table: 'rob_chat_histories', agent: 'Rob' },
    { table: 'n8n_chat_histories', agent: 'Li & Orion' }
  ];

  let allTablesWorking = true;

  for (const chat of chatTables) {
    try {
      const { data, error } = await supabase.from(chat.table).select('*').limit(1);
      if (error) {
        console.log(`   ❌ ${chat.agent} chat memory: ${error.message}`);
        allTablesWorking = false;
      } else {
        console.log(`   ✅ ${chat.agent} chat memory table accessible`);
      }
    } catch (error) {
      console.log(`   ❌ ${chat.agent} chat memory error: ${error.message}`);
      allTablesWorking = false;
    }
  }

  // Test 2: Test Amanda's advice generation capability
  console.log('\n2️⃣ Testing Amanda\'s advice generation...');
  
  const therapeuticScenarios = [
    {
      concern: 'anxiety about job interviews',
      context: 'recent graduate, first time job searching',
      adviceType: 'coping strategies'
    },
    {
      concern: 'dealing with depression and low motivation',
      context: 'working from home, feeling isolated',
      adviceType: 'therapeutic techniques'
    },
    {
      concern: 'relationship conflicts with partner',
      context: 'communication issues, frequent arguments',
      adviceType: 'immediate support'
    },
    {
      concern: 'stress management for work pressure',
      context: 'high-pressure job, long hours',
      adviceType: 'long-term management'
    }
  ];

  let allAdviceTestsPassed = true;

  for (const scenario of therapeuticScenarios) {
    console.log(`\n   🧪 Testing: "${scenario.concern}"`);
    console.log(`      Context: ${scenario.context}`);
    console.log(`      Advice Type: ${scenario.adviceType}`);

    try {
      // Search for relevant therapeutic information (simulating the tool's process)
      const searchQueries = [
        `${scenario.concern} therapy treatment`,
        `${scenario.concern} coping strategies`,
        `${scenario.concern} therapeutic techniques`,
        `${scenario.concern} ${scenario.adviceType}`,
        `${scenario.concern} ${scenario.context}`
      ];

      let allRelevantDocs = [];
      
      for (const query of searchQueries) {
        // Generate embedding using Ollama
        const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'nomic-embed-text:latest',
            prompt: query,
          }),
        });

        if (!embeddingResponse.ok) {
          console.log(`      ❌ Failed to generate embedding for: ${query}`);
          continue;
        }

        const embeddingData = await embeddingResponse.json();
        const queryEmbedding = embeddingData.embedding;

        const { data, error } = await supabase.rpc('match_mental_health_documents', {
          query_embedding: queryEmbedding,
          match_threshold: 0.25,
          match_count: 3,
        });

        if (!error && data && data.length > 0) {
          allRelevantDocs.push(...data);
        }
      }

      // Remove duplicates and get top results
      const uniqueDocs = allRelevantDocs
        .filter((doc, index, self) => 
          index === self.findIndex(d => d.id === doc.id)
        )
        .sort((a, b) => (b.similarity || 0) - (a.similarity || 0))
        .slice(0, 10);

      if (uniqueDocs.length === 0) {
        console.log(`      ❌ No therapeutic resources found for this scenario`);
        allAdviceTestsPassed = false;
        continue;
      }

      console.log(`      ✅ Found ${uniqueDocs.length} therapeutic resources`);
      console.log(`      📊 Best match similarity: ${uniqueDocs[0].similarity?.toFixed(3)}`);
      console.log(`      📄 Top resource: "${uniqueDocs[0].content?.substring(0, 100)}..."`);

      // Check if resources are relevant to mental health
      const hasRelevantContent = uniqueDocs.some(doc => {
        const content = doc.content.toLowerCase();
        return content.includes('therapy') || content.includes('mental health') || 
               content.includes('anxiety') || content.includes('depression') ||
               content.includes('coping') || content.includes('stress');
      });

      if (hasRelevantContent) {
        console.log(`      ✅ Resources contain relevant therapeutic content`);
      } else {
        console.log(`      ⚠️  Resources may not be specifically therapeutic`);
      }

      console.log(`      🎯 Tool would generate evidence-based advice using:`);
      console.log(`         - ${uniqueDocs.length} therapeutic references`);
      console.log(`         - Evidence-based approaches for ${scenario.concern}`);
      console.log(`         - Specific focus on ${scenario.adviceType}`);
      console.log(`         - Context-aware guidance for ${scenario.context}`);

    } catch (error) {
      console.log(`      ❌ Test failed: ${error.message}`);
      allAdviceTestsPassed = false;
    }
  }

  // Test 3: Test knowledge base search capability
  console.log('\n3️⃣ Testing Amanda\'s knowledge base search...');
  
  const knowledgeQueries = [
    'cognitive behavioral therapy techniques',
    'mindfulness meditation for anxiety',
    'depression treatment approaches',
    'trauma therapy methods'
  ];

  let knowledgeTestsPassed = true;

  for (const query of knowledgeQueries) {
    try {
      console.log(`\n   🔍 Query: "${query}"`);
      
      const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'nomic-embed-text:latest',
          prompt: query,
        }),
      });

      const embeddingData = await embeddingResponse.json();
      const queryEmbedding = embeddingData.embedding;

      const { data, error } = await supabase.rpc('match_mental_health_documents', {
        query_embedding: queryEmbedding,
        match_threshold: 0.3,
        match_count: 5
      });

      if (error) {
        console.log(`      ❌ Error: ${error.message}`);
        knowledgeTestsPassed = false;
      } else {
        console.log(`      ✅ Found ${data ? data.length : 0} results`);
        if (data && data.length > 0) {
          console.log(`      🎯 Best match (${data[0].similarity?.toFixed(3)}): "${data[0].content?.substring(0, 80)}..."`);
        }
      }
    } catch (error) {
      console.log(`      ❌ Query failed: ${error.message}`);
      knowledgeTestsPassed = false;
    }
  }

  // Final summary
  console.log('\n' + '='.repeat(60));
  if (allTablesWorking && allAdviceTestsPassed && knowledgeTestsPassed) {
    console.log('🎉 SUCCESS! Amanda\'s Enhanced Capabilities Are Ready!');
    console.log('');
    console.log('✅ CHAT MEMORY:');
    console.log('   • All agent chat history tables are accessible');
    console.log('   • n8n_chat_histories table ready for Li and Orion');
    console.log('   • Persistent memory across conversations enabled');
    console.log('');
    console.log('✅ AMANDA\'S THERAPEUTIC CAPABILITIES:');
    console.log('   • Knowledge base search working for mental health topics');
    console.log('   • Advice generation finds relevant therapeutic resources');
    console.log('   • Evidence-based guidance system operational');
    console.log('   • Context-aware therapeutic recommendations');
    console.log('');
    console.log('🔧 WHAT AMANDA CAN NOW DO:');
    console.log('   • Provide evidence-based therapeutic advice');
    console.log('   • Generate coping strategies from knowledge base');
    console.log('   • Offer context-aware mental health guidance');
    console.log('   • Maintain conversation history for continuity');
    console.log('   • Reference specific therapeutic techniques');
    console.log('');
    console.log('🚀 NEXT STEPS:');
    console.log('1. Run the SQL script to create chat memory tables');
    console.log('2. Restart your Next.js server to load Amanda\'s new tools');
    console.log('3. Test Amanda with therapeutic advice requests');
    console.log('4. Configure Li and Orion to use n8n_chat_histories table');
  } else {
    console.log('⚠️  Some issues found. Please check the errors above.');
    if (!allTablesWorking) console.log('   • Chat memory tables need setup');
    if (!allAdviceTestsPassed) console.log('   • Advice generation needs attention');
    if (!knowledgeTestsPassed) console.log('   • Knowledge base search needs fixing');
  }
  console.log('='.repeat(60));
}

testAmandaEnhancements().catch(console.error);
