{"name": "<PERSON>", "nodes": [{"parameters": {"tableName": "cody_chat_histories"}, "id": "d15c95a7-8ef2-4a9a-9ea3-611b58835167", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-740, 240], "notesInFlow": false, "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId}}", "type": "string"}]}, "options": {}}, "id": "4d8621ab-22d3-41b4-827a-914315185d32", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-940, 0]}, {"parameters": {"public": true, "options": {}}, "id": "71a2fba9-5719-409a-86ac-33773360d255", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1200, 0], "webhookId": "e104e40e-6134-4825-a6f0-8a646d882662"}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to fetch all available documents, including the table schema if the file is a CSV or Excel file.", "operation": "select", "schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "value": "coding_database", "mode": "list", "cachedResultName": "coding_database"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-660, 220], "id": "e2207fe0-ca93-42b9-adf4-a9ec30351d47", "name": "List Documents", "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Given a file ID, fetches the text from the document.", "operation": "execute<PERSON>uery", "query": "SELECT \n    string_agg(text, ' ') as document_text\nFROM documents_pg\n  WHERE metadata->>'file_id' = $1\nGROUP BY metadata->>'file_id';", "options": {"queryReplacement": "={{ $fromAI('file_id') }}"}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-520, 220], "id": "e48d574f-c37f-463d-b4d7-50b20d55362a", "name": "Get File Contents", "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Run a SQL query - use this to query from the document_rows table once you know the file ID (which is the file path) you are querying. dataset_id is the file_id (file path) and you are always using the row_data for filtering, which is a jsonb field that has all the keys from the file schema given in the document_metadata table.\n\nExample query:\n\nSELECT AVG((row_data->>'revenue')::numeric)\nFROM document_rows\nWHERE dataset_id = '/data/shared/document.csv';\n\nExample query 2:\n\nSELECT \n    row_data->>'category' as category,\n    SUM((row_data->>'sales')::numeric) as total_sales\nFROM dataset_rows\nWHERE dataset_id = '/data/shared/document2.csv'\nGROUP BY row_data->>'category';", "operation": "execute<PERSON>uery", "query": "{{ $fromAI('sql_query') }}", "options": {}}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.5, "position": [-360, 220], "id": "d60fd91f-d2e1-4c05-82b9-11d05d9f78ab", "name": "Query Document Rows", "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [-200, 360], "id": "2e2431fa-8382-4f83-9804-6c0a972ee904", "name": "Embeddings Ollama1", "credentials": {"ollamaApi": {"id": "RUmxMwRqnis8wiP5", "name": "Ollama account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolDescription": "=This node serves as <PERSON> the AI coding agent’s semantic memory system. It retrieves relevant programming content from the coding_database vector store using natural language queries. <PERSON> uses this node to access documentation, code snippets, tutorials, and technical explanations—allowing it to provide accurate, context-aware development support. The vector store enables <PERSON> to go beyond keyword search by understanding the meaning behind developer queries and returning the most relevant results from its embedded knowledge base.", "tableName": {"__rl": true, "value": "coding_database", "mode": "list", "cachedResultName": "coding_database"}, "options": {"queryName": "match_coding_database"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.3, "position": [-120, 220], "id": "d50fc704-82dd-4113-ac42-20fbb7434577", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "cvJe6aBnvDVB655o", "name": "Supabase account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are <PERSON>, a senior-level AI coding assistant and mentor. Your purpose is to help users solve programming and technical problems using both your coding expertise and a document-based knowledge base. These documents include both text documents (e.g., PDFs, TXT, DOCX) and structured data files (e.g., CSV, Excel).\n\nYou do not merely provide answers — you act like a seasoned engineering mentor: you write clean, correct code, explain when necessary, and guide users through complex reasoning when problems arise.\n\n🧠 Your Capabilities\nYou have access to the following tools:\n\nRAG (Retrieval-Augmented Generation):\nSearch through all available documents in the documents table using semantic understanding.\n\nDocument Metadata Lookup (document_metadata):\nView document summaries, types, or tags to decide which files to explore.\n\nText Extraction Tool:\nExtract full text from a specific document for deeper inspection.\n\nSQL Query Tool (document_rows):\nQuery structured tabular data for answers involving totals, comparisons, filters, or numeric analysis.\n\nCode Writing:\nWrite accurate, readable, idiomatic code in any major programming language. Use clear formatting, good naming conventions, and add comments if useful.\n\n🎯 Your Responsibilities\nAlways start with a RAG search unless:\n\nThe user asks you to inspect or extract from a specific document\n\nThe question clearly involves structured tabular data requiring SQL (e.g., “sum of sales,” “top 5 entries,” etc.)\n\nIf RAG doesn’t help:\n\nUse document_metadata to inspect available files\n\nPick relevant ones and analyze them directly using text extraction or SQL queries\n\nThen respond with your findings and reasoning\n\nWhen asked for code:\n\nWrite functional, production-quality code with necessary imports, structure, and comments\n\nInclude reasoning if the task is complex or if it’s useful to teach the user\n\nAdjust your response based on the user’s experience (beginner-friendly if needed)\n\n💬 Communication Style\nUse a warm, mentor-like tone — encouraging, informative, and professional\n\nExplain your reasoning when it improves understanding or helps solve bugs\n\nFormat code clearly using triple backticks and language tags (```python, ```sql, etc.)\n\nAnnotate tricky code parts with comments\n\nAvoid long-winded theory unless asked — focus on practical help\n\n✅ Always:\nSay how you arrived at an answer (e.g., “This was retrieved via RAG,” or “I queried the tabular data using SQL”)\n\nTell the user when no answer was found:\n\n“I wasn’t able to find an exact match in the documents, but here’s a best-practice approach…”\n\n❌ Never:\nFabricate answers or guess — accuracy and honesty come first\n\nIgnore SQL or metadata tools when they’re more appropriate than RAG\n\nSkip explanations if the user is likely to benefit from mentoring\n\nBe robotic or overly formal — act like a skilled, supportive developer teammate\n\nFinal Principle:\n\nAlways respond like a thoughtful mentor: insightful, efficient, and technically grounded — using your tools, knowledge, and reasoning to deliver real help, not just answers."}}, "id": "98afd2d8-c625-4ad1-8b01-a75828d69c28", "name": "<PERSON>", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-720, 0]}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-860, 220], "id": "63dd3dbe-c5bd-43d6-b254-cd0a30f9dd74", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "jVDmIGMg9UvFeaZQ", "name": "OpenAi account"}}}, {"parameters": {"html": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [-300, 0], "id": "494d6073-aa0c-48a3-87d9-bcf99849b1e4", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "// Loop through all input items\nconst output = [];\n\nfor (const item of $input.all()) {\n  const text = item.json.text || item.json.message || item.json.body || Object.values(item.json).join(' ') || '';\n\n  const snippets = [];\n\n  // 1. Triple backtick blocks ```lang\\ncode```\n  const tripleBacktickRegex = /```(?:\\w*\\n)?([\\s\\S]*?)```/g;\n  let match;\n  while ((match = tripleBacktickRegex.exec(text)) !== null) {\n    snippets.push(match[1].trim());\n  }\n\n  // 2. Backslash-prefixed blocks (\\js, \\jsx, \\py, etc.)\n  const slashLangRegex = /\\\\(js|jsx|ts|tsx|py|rb|go|sh|html|css|sql)\\s+([\\s\\S]*?)(?=(\\\\\\w|\\n\\d+\\.|\\n[A-Z]|\\n{2,}))/gi;\n  while ((match = slashLangRegex.exec(text)) !== null) {\n    snippets.push(match[2].trim());\n  }\n\n  // 3. Curly-braced or indented patterns\n  const curlyBracedRegex = /((?:[^\\n]*\\{[\\s\\S]*?\\}))/g;\n  while ((match = curlyBracedRegex.exec(text)) !== null) {\n    snippets.push(match[1].trim());\n  }\n\n  // Filter and push\n  const uniqueSnippets = [...new Set(snippets)].filter(Boolean);\n\n  if (uniqueSnippets.length === 0) {\n    output.push({ json: { code: null, note: 'No code found', inputPreview: text.slice(0, 150) } });\n  } else {\n    for (const code of uniqueSnippets) {\n      output.push({ json: { code } });\n    }\n  }\n}\n\nreturn output;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-140, 0], "id": "7c6d0171-899f-497e-bae5-003c72cb6a11", "name": "Code"}, {"parameters": {"path": "14f18b4f-c52b-41fc-8d73-900a46973fc3", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1220, 260], "id": "d5c2b504-cdf9-4058-9cc0-38d95d5acf54", "name": "Webhook", "webhookId": "14f18b4f-c52b-41fc-8d73-900a46973fc3"}], "pinData": {}, "connections": {"Postgres Chat Memory": {"ai_memory": [[{"node": "<PERSON>", "type": "ai_memory", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "List Documents": {"ai_tool": [[{"node": "<PERSON>", "type": "ai_tool", "index": 0}]]}, "Get File Contents": {"ai_tool": [[{"node": "<PERSON>", "type": "ai_tool", "index": 0}]]}, "Query Document Rows": {"ai_tool": [[{"node": "<PERSON>", "type": "ai_tool", "index": 0}]]}, "Embeddings Ollama1": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "<PERSON>", "type": "ai_tool", "index": 0}]]}, "Cody": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "<PERSON>", "type": "ai_languageModel", "index": 0}]]}, "Markdown": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "214ae461-f7eb-4246-814a-c64561f3371", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c8210f6b00e31631c22a2f4d410fbe8193ca1dd4b3ba6e91b2301c1e5e3b7e0b"}, "id": "Mq57tleDBanDxipt", "tags": [{"createdAt": "2025-06-28T09:42:25.716Z", "updatedAt": "2025-06-28T09:42:25.716Z", "id": "1LunJK5nKEskdi2Q", "name": "coder"}]}