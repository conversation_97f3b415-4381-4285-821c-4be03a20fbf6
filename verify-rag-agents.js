const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function verifyRAGAgents() {
  console.log('🔍 Final Verification of RAG Agents Database Setup...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Test embedding with 768 dimensions (matching your Ollama nomic-embed-text model)
  const testEmbedding = new Array(768).fill(0.1);

  const agents = [
    { 
      name: '<PERSON> (Mental Health)', 
      table: 'mental_health_documents', 
      rpc: 'match_mental_health_documents',
      description: 'Mental health and therapy knowledge base'
    },
    { 
      name: '<PERSON> (Coding)', 
      table: 'coding_database', 
      rpc: 'match_coding_database',
      description: 'Programming and technical documentation'
    },
    { 
      name: '<PERSON> (Job Search)', 
      table: 'job_search_database', 
      rpc: 'match_job_search_database',
      description: 'Resume tips and job search strategies'
    }
  ];

  let allPassed = true;

  for (const agent of agents) {
    console.log(`🤖 Testing ${agent.name}...`);
    console.log(`   📝 ${agent.description}`);
    
    // Test table access
    try {
      const { data: tableData, error: tableError } = await supabase
        .from(agent.table)
        .select('id, content')
        .limit(1);

      if (tableError) {
        console.log(`   ❌ Table access failed: ${tableError.message}`);
        allPassed = false;
      } else {
        console.log(`   ✅ Table '${agent.table}' accessible`);
        console.log(`   📊 Has data: ${tableData && tableData.length > 0 ? 'Yes' : 'No'}`);
      }
    } catch (error) {
      console.log(`   ❌ Table test error: ${error.message}`);
      allPassed = false;
    }

    // Test RPC function
    try {
      const { data: rpcData, error: rpcError } = await supabase.rpc(agent.rpc, {
        query_embedding: testEmbedding,
        match_threshold: 0.1, // Lower threshold for testing
        match_count: 3
      });

      if (rpcError) {
        console.log(`   ❌ RPC function failed: ${rpcError.message}`);
        allPassed = false;
      } else {
        console.log(`   ✅ RPC function '${agent.rpc}' working`);
        console.log(`   📊 Test query returned: ${rpcData ? rpcData.length : 0} results`);
        
        if (rpcData && rpcData.length > 0) {
          console.log(`   📄 Sample result: "${rpcData[0].content?.substring(0, 100)}..."`);
        }
      }
    } catch (error) {
      console.log(`   ❌ RPC test error: ${error.message}`);
      allPassed = false;
    }

    console.log(''); // Empty line for readability
  }

  // Test chat memory tables
  console.log('💬 Testing chat memory tables...');
  const chatTables = [
    { table: 'amanda_chat_histories', agent: 'Amanda' },
    { table: 'cody_chat_histories', agent: 'Cody' },
    { table: 'scout_chat_histories', agent: 'Scott' }
  ];

  for (const chat of chatTables) {
    try {
      const { data, error } = await supabase.from(chat.table).select('*').limit(1);
      if (error) {
        console.log(`   ❌ ${chat.agent} chat memory: ${error.message}`);
        allPassed = false;
      } else {
        console.log(`   ✅ ${chat.agent} chat memory table accessible`);
      }
    } catch (error) {
      console.log(`   ❌ ${chat.agent} chat memory error: ${error.message}`);
      allPassed = false;
    }
  }

  console.log('\n' + '='.repeat(60));
  if (allPassed) {
    console.log('🎉 SUCCESS! All RAG agents are properly connected to Supabase!');
    console.log('\n📋 Summary:');
    console.log('✅ All database tables are accessible');
    console.log('✅ All RPC functions are working');
    console.log('✅ All chat memory tables are ready');
    console.log('✅ Vector embeddings are using correct 768 dimensions');
    console.log('\n🚀 Your RAG agents (Amanda, Cody, and Scott) should now work properly!');
  } else {
    console.log('⚠️  Some issues were found. Please review the errors above.');
    console.log('\n🔧 Next steps:');
    console.log('1. Run the SQL script (fix-supabase-setup.sql) in your Supabase SQL Editor');
    console.log('2. Ensure your embedding model outputs 768-dimensional vectors');
    console.log('3. Re-run this verification script');
  }
  console.log('='.repeat(60));
}

verifyRAGAgents().catch(console.error);
