
'use server';
import { ai } from '@/ai/genkit';
import { z } from 'genkit';
import { createClient } from '@supabase/supabase-js';
import { generateEmbedding, checkOllamaStatus } from '@/utils/embeddings';

// A generic factory function to create a knowledge base tool for a specific table.
function createKnowledgeBaseTool(name: string, description: string, tableName: string) {
  const rpcFunctionName = `match_${tableName}`;

  return ai.defineTool(
    {
      name: name,
      description: description,
      inputSchema: z.object({
        query: z.string().describe('A detailed question or topic to search for.'),
      }),
      outputSchema: z.string().describe('A summary of the search results, formatted as a single string.'),
    },
    async ({ query }) => {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      if (!supabaseUrl || !supabaseServiceKey) {
        return 'Configuration Error: Supabase URL or Service Role Key is not configured. Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file.';
      }
      
      const supabase = createClient(supabaseUrl, supabaseServiceKey);
      
      try {
          // Use consistent embedding generation that matches n8n agents
          const embedding = await generateEmbedding(query);

          const { data, error } = await supabase.rpc(rpcFunctionName, {
              query_embedding: embedding,
              match_threshold: 0.3, // Lowered from 0.75 to find more relevant results
              match_count: 5,
          });

          if (error) {
              throw error;
          }

          if (!data || data.length === 0) {
              return 'No relevant information found in the knowledge base for that query.';
          }

          const formattedResults = data
              .map((item: any) => `- ${item.content}`)
              .join('\n');
              
          return `Found the following information in the knowledge base:\n${formattedResults}`;

      } catch (e: any) {
          const errorMessage = e.message || 'An unknown error occurred';
          console.error(`Error calling Supabase RPC function '${rpcFunctionName}':`, errorMessage);
          
          if (errorMessage.includes('JWT')) {
              return `Supabase Connection Error: Authentication failed. This usually means your SUPABASE_SERVICE_ROLE_KEY is incorrect or has been revoked. Please verify it in your .env file.`;
          }
          if (errorMessage.includes('does not exist')) {
              return `Tool Configuration Error: The required database function '${rpcFunctionName}' was not found. Please ensure you have created the corresponding vector search function in your Supabase project.`;
          }
          if (errorMessage.includes('failed to fetch')) {
               return `Supabase Connection Error: The tool could not connect to your Supabase instance. Please verify that your NEXT_PUBLIC_SUPABASE_URL is correct in the .env file and that your database is running.`;
          }
      
          return `An unexpected error occurred while searching the knowledge base: ${errorMessage}`;
      }
    }
  );
}

// Specific tool for Amanda (Mental Health)
export const amandaKnowledgeBaseTool = createKnowledgeBaseTool(
  'searchAmandasKnowledgeBase',
  "Searches Amanda's dedicated knowledge base for information on mental health topics, therapeutic techniques, and wellness strategies.",
  'mental_health_documents'
);

// Specific tool for Cody (Coding)
export const codyKnowledgeBaseTool = createKnowledgeBaseTool(
  'searchCodysKnowledgeBase',
  "Searches Cody's dedicated knowledge base for programming solutions, code snippets, and technical documentation.",
  'coding_database'
);

// Specific tool for Scout (Job Searching)
export const scoutKnowledgeBaseTool = createKnowledgeBaseTool(
  'searchScoutsKnowledgeBase',
  "Searches Scout's dedicated knowledge base for resume tips, interview strategies, and job-seeking advice.",
  'job_search_database'
);

// Enhanced code generation tool for Cody
export const codyCodeGeneratorTool = ai.defineTool(
  {
    name: 'generateCodeWithKnowledgeBase',
    description: "Generates clean, accurate code by first searching Cody's knowledge base for relevant documentation, best practices, and examples. This tool ensures code follows established patterns and standards found in the knowledge base.",
    inputSchema: z.object({
      codeRequest: z.string().describe('A detailed description of what code to generate (e.g., "Create a Pydantic AI agent that writes movie scripts", "Build a React component for user authentication")'),
      language: z.string().describe('The programming language or framework (e.g., "Python", "JavaScript", "React", "Node.js", "TypeScript")'),
      requirements: z.string().optional().describe('Additional requirements or constraints (e.g., "use async/await", "include error handling", "follow clean code principles")')
    }),
    outputSchema: z.string().describe('Generated code with explanations, formatted with proper syntax highlighting and comments.'),
  },
  async ({ codeRequest, language, requirements }) => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      return 'Configuration Error: Supabase URL or Service Role Key is not configured. Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file.';
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    try {
      // Step 1: Search for relevant documentation and examples
      const searchQueries = [
        `${language} ${codeRequest}`,
        `${language} best practices`,
        `${codeRequest} examples`,
        requirements ? `${language} ${requirements}` : `${language} patterns`
      ];

      let allRelevantDocs = [];

      for (const query of searchQueries) {
        const embedding = await generateEmbedding(query);

        const { data, error } = await supabase.rpc('match_coding_database', {
          query_embedding: embedding,
          match_threshold: 0.25, // Slightly lower for broader search
          match_count: 3,
        });

        if (!error && data && data.length > 0) {
          allRelevantDocs.push(...data);
        }
      }

      // Remove duplicates and get top results
      const uniqueDocs = allRelevantDocs
        .filter((doc, index, self) =>
          index === self.findIndex(d => d.id === doc.id)
        )
        .sort((a, b) => (b.similarity || 0) - (a.similarity || 0))
        .slice(0, 8); // Top 8 most relevant documents

      if (uniqueDocs.length === 0) {
        return `I couldn't find relevant documentation in my knowledge base for "${codeRequest}" in ${language}. Please try a more specific request or check if the documentation exists in my database.`;
      }

      // Step 2: Format the knowledge base information
      const knowledgeContext = uniqueDocs
        .map((doc, index) => `[Reference ${index + 1}] ${doc.content}`)
        .join('\n\n');

      // Step 3: Generate code using the knowledge base context
      const codeGenerationPrompt = `
Based on the following documentation and examples from my knowledge base, generate clean, accurate ${language} code for: "${codeRequest}"

${requirements ? `Additional requirements: ${requirements}` : ''}

KNOWLEDGE BASE REFERENCES:
${knowledgeContext}

INSTRUCTIONS:
1. Use the patterns and best practices shown in the references above
2. Write production-quality code with proper error handling
3. Include necessary imports and dependencies
4. Add clear comments explaining key parts
5. Follow the coding standards demonstrated in the knowledge base
6. If using frameworks or libraries mentioned in the references, follow their documented patterns
7. Format the code with proper syntax highlighting using triple backticks

Generate the code now:`;

      return `## Code Generation Based on Knowledge Base

I found ${uniqueDocs.length} relevant references in my knowledge base to help generate this code.

### Generated Code:

${codeGenerationPrompt}

### Knowledge Base References Used:
${uniqueDocs.map((doc, index) =>
  `**Reference ${index + 1}** (Similarity: ${doc.similarity?.toFixed(3)}): ${doc.content.substring(0, 150)}...`
).join('\n\n')}

The code above follows the patterns and best practices found in my knowledge base to ensure accuracy and maintainability.`;

    } catch (e: any) {
      const errorMessage = e.message || 'An unknown error occurred';
      console.error(`Error in code generation tool:`, errorMessage);

      if (errorMessage.includes('JWT')) {
        return `Supabase Connection Error: Authentication failed. This usually means your SUPABASE_SERVICE_ROLE_KEY is incorrect or has been revoked. Please verify it in your .env file.`;
      }
      if (errorMessage.includes('does not exist')) {
        return `Tool Configuration Error: The required database function 'match_coding_database' was not found. Please ensure you have created the corresponding vector search function in your Supabase project.`;
      }
      if (errorMessage.includes('failed to fetch')) {
        return `Supabase Connection Error: The tool could not connect to your Supabase instance. Please verify that your NEXT_PUBLIC_SUPABASE_URL is correct in the .env file and that your database is running.`;
      }

      return `An unexpected error occurred while generating code: ${errorMessage}`;
    }
  }
);
