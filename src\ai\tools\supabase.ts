
'use server';
import { ai } from '@/ai/genkit';
import { z } from 'genkit';
import { createClient } from '@supabase/supabase-js';
import { generateEmbedding, checkOllamaStatus } from '@/utils/embeddings';

// A generic factory function to create a knowledge base tool for a specific table.
function createKnowledgeBaseTool(name: string, description: string, tableName: string) {
  const rpcFunctionName = `match_${tableName}`;

  return ai.defineTool(
    {
      name: name,
      description: description,
      inputSchema: z.object({
        query: z.string().describe('A detailed question or topic to search for.'),
      }),
      outputSchema: z.string().describe('A summary of the search results, formatted as a single string.'),
    },
    async ({ query }) => {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      if (!supabaseUrl || !supabaseServiceKey) {
        return 'Configuration Error: Supabase URL or Service Role Key is not configured. Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file.';
      }
      
      const supabase = createClient(supabaseUrl, supabaseServiceKey);
      
      try {
          // Use consistent embedding generation that matches n8n agents
          const embedding = await generateEmbedding(query);

          const { data, error } = await supabase.rpc(rpcFunctionName, {
              query_embedding: embedding,
              match_threshold: 0.3, // Lowered from 0.75 to find more relevant results
              match_count: 5,
          });

          if (error) {
              throw error;
          }

          if (!data || data.length === 0) {
              return 'No relevant information found in the knowledge base for that query.';
          }

          const formattedResults = data
              .map((item: any) => `- ${item.content}`)
              .join('\n');
              
          return `Found the following information in the knowledge base:\n${formattedResults}`;

      } catch (e: any) {
          const errorMessage = e.message || 'An unknown error occurred';
          console.error(`Error calling Supabase RPC function '${rpcFunctionName}':`, errorMessage);
          
          if (errorMessage.includes('JWT')) {
              return `Supabase Connection Error: Authentication failed. This usually means your SUPABASE_SERVICE_ROLE_KEY is incorrect or has been revoked. Please verify it in your .env file.`;
          }
          if (errorMessage.includes('does not exist')) {
              return `Tool Configuration Error: The required database function '${rpcFunctionName}' was not found. Please ensure you have created the corresponding vector search function in your Supabase project.`;
          }
          if (errorMessage.includes('failed to fetch')) {
               return `Supabase Connection Error: The tool could not connect to your Supabase instance. Please verify that your NEXT_PUBLIC_SUPABASE_URL is correct in the .env file and that your database is running.`;
          }
      
          return `An unexpected error occurred while searching the knowledge base: ${errorMessage}`;
      }
    }
  );
}

// Specific tool for Amanda (Mental Health)
export const amandaKnowledgeBaseTool = createKnowledgeBaseTool(
  'searchAmandasKnowledgeBase',
  "Searches Amanda's dedicated knowledge base for information on mental health topics, therapeutic techniques, and wellness strategies.",
  'mental_health_documents'
);

// Specific tool for Cody (Coding)
export const codyKnowledgeBaseTool = createKnowledgeBaseTool(
  'searchCodysKnowledgeBase',
  "Searches Cody's dedicated knowledge base for programming solutions, code snippets, and technical documentation.",
  'coding_database'
);

// Specific tool for Scout (Job Searching)
export const scoutKnowledgeBaseTool = createKnowledgeBaseTool(
  'searchScoutsKnowledgeBase',
  "Searches Scout's dedicated knowledge base for resume tips, interview strategies, and job-seeking advice.",
  'job_search_database'
);
