'use client';

import type { Agent } from '@/lib/types';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Label } from './ui/label';
import { Switch } from './ui/switch';
import { Separator } from './ui/separator';
import { ScrollArea } from './ui/scroll-area';

interface AgentConfigProps {
  agent: Agent;
}

export function AgentConfig({ agent }: AgentConfigProps) {
  const [tools, setTools] = useState(agent.tools);

  const handleToolToggle = (toolId: string) => {
    setTools(
      tools.map(tool =>
        tool.id === toolId ? { ...tool, enabled: !tool.enabled } : tool
      )
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Agent Configuration</CardTitle>
        <CardDescription>Manage tools and settings for this agent.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="text-md font-medium mb-2">System Prompt</h3>
          <ScrollArea className="h-32 rounded-md border bg-muted/50 p-3 text-xs">
            <pre className="font-code whitespace-pre-wrap">{agent.systemMessage}</pre>
          </ScrollArea>
        </div>

        {tools.length > 0 && <Separator />}
        
        {tools.length > 0 && (
          <div>
            <h3 className="text-md font-medium mb-2">Tools</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Enable or disable tools that this agent can use.
            </p>
            <div className="space-y-4">
              {tools.map((tool, index) => (
                <div key={tool.id}>
                  <div className="flex items-center justify-between space-x-4">
                    <div className="space-y-0.5">
                      <Label htmlFor={`tool-${tool.id}`} className="font-semibold">
                        {tool.name}
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        {tool.description}
                      </p>
                    </div>
                    <Switch
                      id={`tool-${tool.id}`}
                      checked={tool.enabled}
                      onCheckedChange={() => handleToolToggle(tool.id)}
                      aria-label={`Toggle ${tool.name}`}
                    />
                  </div>
                  {index < tools.length - 1 && <Separator className="mt-4" />}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
