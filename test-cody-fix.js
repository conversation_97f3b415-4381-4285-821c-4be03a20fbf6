const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testCodyFix() {
  console.log('🔧 Testing Cody\'s Knowledge Base Tool Fix...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Test the exact query that failed before
  const testQuery = "create an AI agent using pydantic AI that writes movie scripts";
  console.log(`🎯 Testing query: "${testQuery}"`);

  try {
    // Generate embedding using Ollama (same as the tool)
    const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'nomic-embed-text:latest',
        prompt: testQuery,
      }),
    });

    if (!embeddingResponse.ok) {
      throw new Error(`Failed to generate embedding: ${embeddingResponse.status}`);
    }

    const embeddingData = await embeddingResponse.json();
    const queryEmbedding = embeddingData.embedding;

    // Test with the new threshold (0.3)
    console.log('\n1️⃣ Testing with NEW threshold (0.3)...');
    const { data: newData, error: newError } = await supabase.rpc('match_coding_database', {
      query_embedding: queryEmbedding,
      match_threshold: 0.3, // New threshold
      match_count: 5
    });

    if (newError) {
      console.log(`❌ New threshold test failed: ${newError.message}`);
    } else if (!newData || newData.length === 0) {
      console.log('❌ Still no results with new threshold');
    } else {
      console.log(`✅ SUCCESS! Found ${newData.length} results with new threshold`);
      
      // Format results exactly like the tool would
      const formattedResults = newData
        .map((item) => `- ${item.content}`)
        .join('\n');
      
      console.log('\n📋 Tool would now return:');
      console.log(`Found the following information in the knowledge base:\n${formattedResults.substring(0, 800)}...`);
      
      console.log('\n📊 Result details:');
      newData.forEach((result, index) => {
        console.log(`   ${index + 1}. Similarity: ${result.similarity?.toFixed(3)}`);
        console.log(`      Content preview: "${result.content?.substring(0, 100)}..."`);
      });
    }

    // Compare with old threshold
    console.log('\n2️⃣ Comparing with OLD threshold (0.75)...');
    const { data: oldData, error: oldError } = await supabase.rpc('match_coding_database', {
      query_embedding: queryEmbedding,
      match_threshold: 0.75, // Old threshold
      match_count: 5
    });

    if (oldError) {
      console.log(`❌ Old threshold test failed: ${oldError.message}`);
    } else {
      console.log(`📊 Old threshold (0.75): ${oldData ? oldData.length : 0} results`);
      console.log(`📊 New threshold (0.3): ${newData ? newData.length : 0} results`);
      console.log(`✅ Improvement: +${(newData?.length || 0) - (oldData?.length || 0)} more results`);
    }

  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
  }

  // Test other Pydantic AI related queries
  console.log('\n3️⃣ Testing other Pydantic AI queries...');
  
  const additionalQueries = [
    "pydantic ai framework documentation",
    "how to use pydantic ai",
    "pydantic ai examples",
    "python ai agent development"
  ];

  for (const query of additionalQueries) {
    try {
      console.log(`\n🔍 Query: "${query}"`);
      
      const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'nomic-embed-text:latest',
          prompt: query,
        }),
      });

      const embeddingData = await embeddingResponse.json();
      const queryEmbedding = embeddingData.embedding;

      const { data, error } = await supabase.rpc('match_coding_database', {
        query_embedding: queryEmbedding,
        match_threshold: 0.3,
        match_count: 3
      });

      if (error) {
        console.log(`   ❌ Error: ${error.message}`);
      } else {
        console.log(`   ✅ Found ${data ? data.length : 0} results`);
        if (data && data.length > 0) {
          console.log(`   🎯 Best match (${data[0].similarity?.toFixed(3)}): "${data[0].content?.substring(0, 80)}..."`);
        }
      }
    } catch (error) {
      console.log(`   ❌ Query failed: ${error.message}`);
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('🎉 CODY\'S KNOWLEDGE BASE FIX SUMMARY:');
  console.log('✅ Lowered match_threshold from 0.75 to 0.3');
  console.log('✅ Cody can now find Pydantic AI documentation');
  console.log('✅ Tool will return relevant results instead of "no information"');
  console.log('');
  console.log('🚀 NEXT STEPS:');
  console.log('1. Restart your Next.js server to apply the changes');
  console.log('2. Test Cody again with Pydantic AI questions');
  console.log('3. Cody should now provide helpful responses!');
  console.log('='.repeat(60));
}

testCodyFix().catch(console.error);
