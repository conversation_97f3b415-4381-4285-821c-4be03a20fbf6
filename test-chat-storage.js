const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testChatStorage() {
  console.log('💬 Testing Next.js Chat Storage Implementation...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Test 1: Verify all chat tables exist and are accessible
  console.log('1️⃣ Testing chat table accessibility...');
  
  const chatTables = [
    { table: 'amanda_chat_histories', agent: 'Amanda', agentId: 'NeK3H8lDKNXHFHVV' },
    { table: 'cody_chat_histories', agent: '<PERSON>', agentId: 'Mq57tleDBanDxipt' },
    { table: 'scout_chat_histories', agent: 'Scout', agentId: '4jn4JiOzQZuclHRD' },
    { table: 'rob_chat_histories', agent: '<PERSON>', agentId: 'PfUSVNwHXQy7c6C8' },
    { table: 'n8n_chat_histories', agent: 'Li & Orion', agentId: 'x3M1ATR5WY16vkPd' }
  ];

  let allTablesWorking = true;

  for (const chat of chatTables) {
    try {
      const { data, error } = await supabase.from(chat.table).select('*').limit(1);
      if (error) {
        console.log(`   ❌ ${chat.agent} (${chat.table}): ${error.message}`);
        allTablesWorking = false;
      } else {
        console.log(`   ✅ ${chat.agent} (${chat.table}): Accessible`);
      }
    } catch (error) {
      console.log(`   ❌ ${chat.agent} (${chat.table}): ${error.message}`);
      allTablesWorking = false;
    }
  }

  // Test 2: Test chat message saving functionality
  console.log('\n2️⃣ Testing chat message saving...');
  
  const testSessionId = `test-session-${Date.now()}`;
  const testAgents = [
    { id: 'NeK3H8lDKNXHFHVV', name: 'Amanda', table: 'amanda_chat_histories' },
    { id: 'Mq57tleDBanDxipt', name: 'Cody', table: 'cody_chat_histories' },
    { id: '4jn4JiOzQZuclHRD', name: 'Scout', table: 'scout_chat_histories' },
    { id: 'x3M1ATR5WY16vkPd', name: 'Li', table: 'n8n_chat_histories' }
  ];

  let savingWorking = true;

  for (const agent of testAgents) {
    try {
      console.log(`\n   🧪 Testing ${agent.name}...`);
      
      // Test saving human message
      const humanMessageData = {
        session_id: testSessionId,
        message_type: 'human',
        content: `Test message to ${agent.name}`,
        metadata: { test: true },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // For n8n_chat_histories, add agent_name
      if (agent.table === 'n8n_chat_histories') {
        humanMessageData.agent_name = agent.name;
      }

      const { data: humanData, error: humanError } = await supabase
        .from(agent.table)
        .insert(humanMessageData)
        .select()
        .single();

      if (humanError) {
        console.log(`      ❌ Human message save failed: ${humanError.message}`);
        savingWorking = false;
        continue;
      }

      console.log(`      ✅ Human message saved (ID: ${humanData.id})`);

      // Test saving AI message
      const aiMessageData = {
        session_id: testSessionId,
        message_type: 'ai',
        content: `Hello! This is ${agent.name} responding to your test message.`,
        metadata: { test: true, response_to: humanData.id },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // For n8n_chat_histories, add agent_name
      if (agent.table === 'n8n_chat_histories') {
        aiMessageData.agent_name = agent.name;
      }

      const { data: aiData, error: aiError } = await supabase
        .from(agent.table)
        .insert(aiMessageData)
        .select()
        .single();

      if (aiError) {
        console.log(`      ❌ AI message save failed: ${aiError.message}`);
        savingWorking = false;
        continue;
      }

      console.log(`      ✅ AI message saved (ID: ${aiData.id})`);

    } catch (error) {
      console.log(`      ❌ ${agent.name} test failed: ${error.message}`);
      savingWorking = false;
    }
  }

  // Test 3: Test chat history loading
  console.log('\n3️⃣ Testing chat history loading...');
  
  let loadingWorking = true;

  for (const agent of testAgents) {
    try {
      console.log(`\n   📖 Loading ${agent.name}'s chat history...`);
      
      let query = supabase
        .from(agent.table)
        .select('*')
        .eq('session_id', testSessionId)
        .order('created_at', { ascending: true });

      // For n8n_chat_histories, also filter by agent_name
      if (agent.table === 'n8n_chat_histories') {
        query = query.eq('agent_name', agent.name);
      }

      const { data, error } = await query;

      if (error) {
        console.log(`      ❌ Loading failed: ${error.message}`);
        loadingWorking = false;
        continue;
      }

      console.log(`      ✅ Loaded ${data ? data.length : 0} messages`);
      
      if (data && data.length > 0) {
        console.log(`      📄 First message: "${data[0].content?.substring(0, 50)}..."`);
        console.log(`      📄 Last message: "${data[data.length - 1].content?.substring(0, 50)}..."`);
      }

    } catch (error) {
      console.log(`      ❌ ${agent.name} loading test failed: ${error.message}`);
      loadingWorking = false;
    }
  }

  // Test 4: Test session cleanup
  console.log('\n4️⃣ Testing session cleanup...');
  
  let cleanupWorking = true;

  for (const agent of testAgents) {
    try {
      let query = supabase
        .from(agent.table)
        .delete()
        .eq('session_id', testSessionId);

      // For n8n_chat_histories, also filter by agent_name
      if (agent.table === 'n8n_chat_histories') {
        query = query.eq('agent_name', agent.name);
      }

      const { error } = await query;

      if (error) {
        console.log(`   ❌ ${agent.name} cleanup failed: ${error.message}`);
        cleanupWorking = false;
      } else {
        console.log(`   ✅ ${agent.name} test messages cleaned up`);
      }

    } catch (error) {
      console.log(`   ❌ ${agent.name} cleanup error: ${error.message}`);
      cleanupWorking = false;
    }
  }

  // Final summary
  console.log('\n' + '='.repeat(60));
  if (allTablesWorking && savingWorking && loadingWorking && cleanupWorking) {
    console.log('🎉 SUCCESS! Next.js Chat Storage is Fully Working!');
    console.log('');
    console.log('✅ CHAT STORAGE CAPABILITIES:');
    console.log('   • All chat history tables are accessible');
    console.log('   • Messages can be saved to Supabase');
    console.log('   • Chat history can be loaded from Supabase');
    console.log('   • Session management is working');
    console.log('   • Agent-specific table routing is correct');
    console.log('');
    console.log('🔧 WHAT YOUR NEXT.JS APP CAN NOW DO:');
    console.log('   • Save every conversation to Supabase');
    console.log('   • Load previous conversations when you return');
    console.log('   • Maintain separate chat histories for each agent');
    console.log('   • Continue conversations across browser sessions');
    console.log('   • Store conversation metadata and timestamps');
    console.log('');
    console.log('🚀 NEXT STEPS:');
    console.log('1. Restart your Next.js server to apply the changes');
    console.log('2. Start a conversation with any agent');
    console.log('3. Refresh the page - your conversation should continue!');
    console.log('4. Check your Supabase tables to see stored messages');
  } else {
    console.log('⚠️  Some issues found. Please check the errors above.');
    if (!allTablesWorking) console.log('   • Chat tables need setup or have access issues');
    if (!savingWorking) console.log('   • Message saving functionality needs fixing');
    if (!loadingWorking) console.log('   • Chat history loading needs attention');
    if (!cleanupWorking) console.log('   • Session cleanup has issues');
  }
  console.log('='.repeat(60));
}

testChatStorage().catch(console.error);
