{"name": "Li", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.data }}", "options": {"systemMessage": "You are <PERSON>, an expert professional email communication specialist. Your role is to compose polished, professional emails that maintain excellent communication standards.\n\n## Core Responsibilities:\n- Craft professional, well-structured emails\n- Maintain appropriate tone based on context and recipient\n- Ensure clear communication with proper etiquette\n- Include all necessary elements: greeting, body, closing, signature\n\n## Communication Standards:\n- **Tone Adaptation**: Adjust formality based on recipient relationship and specified preference\n- **Clarity**: Use clear, concise language avoiding unnecessary jargon\n- **Structure**: Include proper greeting, context, main message, call-to-action (if needed), and professional closing\n- **Professionalism**: Maintain courteous, respectful tone throughout\n\n## Email Composition Guidelines:\n- Start with appropriate greeting (Dear [Name], Hi [Name], etc.)\n- Provide context if necessary\n- Present main message clearly and concisely\n- Include specific call-to-action if required\n- End with professional closing and signature line\n- Proofread for grammar, spelling, and clarity\n\n## Output Format:\nProvide the complete email ready to send, including:\n- Subject line (if different from provided)\n- Full email body with proper formatting\n- Professional signature\n\n## Important Notes:\n- Never make commitments on behalf of the user\n- Maintain professional boundaries\n- Ask for clarification if critical information is missing\n- Suggest improvements to enhance communication effectiveness"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1480, 360], "id": "08e5ab46-fbe3-41f2-9748-b4aa0c512edb", "name": "AI Agent"}, {"parameters": {"public": true, "initialMessages": "", "options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 460], "id": "c2d0c0ac-35fe-4426-97a0-6ecd670310a1", "name": "When chat message received", "webhookId": "77cc3365-69a7-4da6-9c45-e26c0f3d8a93"}, {"parameters": {"assignments": {"assignments": [{"id": "df6daff3-9261-41be-a0cb-6a24493ba5d3", "name": "chatInput", "value": "={{ $json.chatInput }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 360], "id": "641ac515-79cf-4bf1-80b2-0c1a7a415d68", "name": "Edit Fields3"}, {"parameters": {"sendTo": "={{ $('Email Information Extractor').item.json.output['email address'] }}", "subject": "={{ $('Email Information Extractor').item.json.output.subject }}", "emailType": "text", "message": "={{ $json.output }}", "options": {"senderName": "=Ebo"}}, "id": "0011e11c-a4c4-41c5-b9b9-1bb310e51662", "name": "Send Email", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [1860, 360], "webhookId": "31c7e14d-80f2-4fbe-9351-3bfda38657d6", "credentials": {"gmailOAuth2": {"id": "LBqAyCFAWGTV7bKB", "name": "Gmail account"}}}, {"parameters": {"text": "={{ $('Edit Fields3').item.json.chatInput }}", "attributes": {"attributes": [{"name": "email address", "description": "An email address is a unique digital identifier used to send and receive electronic messages. It typically belongs to a person or organization. The address should be valid, well-formatted, and look like:  pgsql <NAME_EMAIL> Examples:  <EMAIL>  <EMAIL>  <EMAIL>  Rules:  Must contain exactly one @ symbol  Must have a domain and extension (e.g., gmail.com)  No spaces, commas, or special characters outside of . and _  If no email is mentioned in the input, return null", "required": true}, {"name": "subject", "description": "The subject is a brief and clear summary of the main purpose of the email. It tells the recipient what the message is about at a glance. Think of it as the headline of your email.  A good subject:  Is short (usually under 10 words)  Is specific, not vague  Reflects the tone and intent of the email (e.g., friendly, formal, urgent)  Examples:  Meeting Rescheduled to Friday  Follow-Up on Project Proposal  Welcome to the Team!  Quick Question About Today’s Session", "required": true}, {"name": "message", "description": "The message is the main content of the email. It should be a complete, well-written message that:  Clearly communicates the intended information  Matches the tone and purpose of the input  Includes a polite opening and closing if appropriate  Feels like something you could actually hit \"Send\" on  A good message:  Can be formal or casual, depending on the context  Is written in full sentences  Avoids jargon, ambiguity, or filler words  Example:  text Copy Edit Hi <PERSON>,  I hope you're doing well. I just wanted to follow up on the product launch. Let me know if there's anything you need from my side.  Best regards,   <PERSON>", "required": true}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\nYour job is to analyze the user's message and return:\n\nThe email address (if mentioned)\n\nThe subject of the email (a short phrase that summarizes the main intent)\n\n\nIf the email address is not present in the text, leave it as null. Use natural language understanding to infer the subjectn if not explicitly labeled."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.2, "position": [900, 360], "id": "75358c63-9dc5-4885-a0a9-b7c2165b3130", "name": "Email Information Extractor"}, {"parameters": {"html": "={{ $json.output.message }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [1280, 360], "id": "228cb286-8a97-4f28-9e91-bf1395c571a1", "name": "<PERSON><PERSON>"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1440, 640], "id": "ef7475f4-277d-46eb-97af-751d0916b31e", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "jVDmIGMg9UvFeaZQ", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [800, 600], "id": "09cb0c10-3579-4cf7-881a-da4c65ef8c60", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "jVDmIGMg9UvFeaZQ", "name": "OpenAi account"}}}, {"parameters": {"path": "5a1996ab-8135-433f-97b5-0b4d0fc1ed46", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [200, 600], "id": "89ce0a7a-0b00-4be9-9319-8feb3b2ca076", "name": "Webhook", "webhookId": "5a1996ab-8135-433f-97b5-0b4d0fc1ed46"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}, "Edit Fields3": {"main": [[{"node": "Email Information Extractor", "type": "main", "index": 0}]]}, "Email Information Extractor": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Markdown": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Email Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Webhook": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e489d00f-5716-43cb-9360-e4874dfee7b2", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c8210f6b00e31631c22a2f4d410fbe8193ca1dd4b3ba6e91b2301c1e5e3b7e0b"}, "id": "x3M1ATR5WY16vkPd", "tags": [{"createdAt": "2025-06-28T09:42:01.095Z", "updatedAt": "2025-06-28T09:42:01.095Z", "id": "byWR743AZ6onw2w6", "name": "email"}]}