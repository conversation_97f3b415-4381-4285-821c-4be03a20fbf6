// Test the Next.js RAG tools with consistent embedding
require('dotenv').config();

async function testNextJSTools() {
  console.log('🔍 Testing Next.js RAG Tools with Ollama Embeddings...\n');

  // Test the embedding utility directly
  console.log('1️⃣ Testing embedding utility...');
  try {
    // Import the embedding utility
    const { generateEmbedding } = require('./src/utils/embeddings.ts');
    
    const testQuery = "How to manage anxiety and stress?";
    console.log(`Query: "${testQuery}"`);
    
    const embedding = await generateEmbedding(testQuery);
    
    if (Array.isArray(embedding) && embedding.length === 768) {
      console.log(`✅ Embedding generated: ${embedding.length} dimensions`);
      console.log(`   First few values: [${embedding.slice(0, 5).map(v => v.toFixed(3)).join(', ')}...]`);
    } else {
      console.log(`❌ Unexpected embedding format: ${typeof embedding}, length: ${embedding?.length}`);
    }
  } catch (error) {
    console.log(`❌ Embedding utility test failed: ${error.message}`);
    console.log('   This might be due to TypeScript import in Node.js');
  }

  // Test Supabase tools directly
  console.log('\n2️⃣ Testing Supabase RPC functions directly...');
  
  const { createClient } = require('@supabase/supabase-js');
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Generate embedding using Ollama directly
  const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'nomic-embed-text:latest',
      prompt: 'How to write clean code and best practices?',
    }),
  });

  const embeddingData = await embeddingResponse.json();
  const queryEmbedding = embeddingData.embedding;

  // Test all three agents
  const agents = [
    { name: 'Amanda', rpc: 'match_mental_health_documents', query: 'anxiety coping strategies' },
    { name: 'Cody', rpc: 'match_coding_database', query: 'clean code best practices' },
    { name: 'Scott', rpc: 'match_job_search_database', query: 'resume writing tips' }
  ];

  for (const agent of agents) {
    try {
      // Generate specific embedding for each agent's query
      const agentEmbeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'nomic-embed-text:latest',
          prompt: agent.query,
        }),
      });

      const agentEmbeddingData = await agentEmbeddingResponse.json();
      const agentQueryEmbedding = agentEmbeddingData.embedding;

      const { data, error } = await supabase.rpc(agent.rpc, {
        query_embedding: agentQueryEmbedding,
        match_threshold: 0.1,
        match_count: 2
      });

      if (error) {
        console.log(`❌ ${agent.name}: ${error.message}`);
      } else {
        console.log(`✅ ${agent.name}: Found ${data ? data.length : 0} results`);
        if (data && data.length > 0) {
          console.log(`   📄 Best match: "${data[0].content?.substring(0, 80)}..."`);
          console.log(`   🎯 Similarity: ${data[0].similarity?.toFixed(3)}`);
        }
      }
    } catch (error) {
      console.log(`❌ ${agent.name}: ${error.message}`);
    }
  }

  console.log('\n3️⃣ Testing tool integration...');
  
  // Simulate how the tools would be called from Next.js
  const toolTests = [
    { name: 'Amanda Knowledge Base', table: 'mental_health_documents', query: 'depression treatment options' },
    { name: 'Cody Knowledge Base', table: 'coding_database', query: 'JavaScript async await patterns' },
    { name: 'Scott Knowledge Base', table: 'job_search_database', query: 'interview preparation checklist' }
  ];

  for (const test of toolTests) {
    try {
      // Generate embedding
      const toolEmbeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'nomic-embed-text:latest',
          prompt: test.query,
        }),
      });

      const toolEmbeddingData = await toolEmbeddingResponse.json();
      const toolQueryEmbedding = toolEmbeddingData.embedding;

      // Call RPC function
      const rpcFunctionName = `match_${test.table}`;
      const { data, error } = await supabase.rpc(rpcFunctionName, {
        query_embedding: toolQueryEmbedding,
        match_threshold: 0.75,
        match_count: 5
      });

      if (error) {
        console.log(`❌ ${test.name}: ${error.message}`);
      } else if (!data || data.length === 0) {
        console.log(`⚠️  ${test.name}: No results found (try lowering match_threshold)`);
      } else {
        console.log(`✅ ${test.name}: ${data.length} results found`);
        
        // Format results like the actual tool would
        const formattedResults = data
          .map((item) => `- ${item.content}`)
          .join('\n');
        
        console.log(`   📋 Sample formatted output:`);
        console.log(`   Found the following information in the knowledge base:`);
        console.log(`   ${formattedResults.substring(0, 200)}...`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('🎯 EMBEDDING CONSISTENCY STATUS:');
  console.log('✅ Ollama nomic-embed-text: 768 dimensions');
  console.log('✅ Supabase database vectors: 768 dimensions');
  console.log('✅ n8n agents: Use Ollama nomic-embed-text');
  console.log('✅ Next.js tools: Now configured to use Ollama nomic-embed-text');
  console.log('');
  console.log('🚀 NEXT STEPS:');
  console.log('1. Your RAG agents should now work consistently');
  console.log('2. Both n8n and Next.js interfaces will use the same embeddings');
  console.log('3. Make sure Ollama stays running for optimal performance');
  console.log('='.repeat(60));
}

testNextJSTools().catch(console.error);
