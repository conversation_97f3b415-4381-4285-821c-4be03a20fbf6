-- Safe Fix for Supabase RAG Agent Database Issues
-- This version safely handles existing functions

-- Step 1: Check what functions exist (informational - you can run this first to see what's there)
-- SELECT proname, pg_get_function_arguments(oid) as arguments 
-- FROM pg_proc 
-- WHERE proname LIKE 'match_%' 
-- ORDER BY proname;

-- Step 2: Drop existing functions using CASCADE to handle all overloads
-- This will remove all versions of these functions regardless of their signatures

DROP FUNCTION IF EXISTS match_mental_health_documents CASCADE;
DROP FUNCTION IF EXISTS match_coding_database CASCADE;
DROP FUNCTION IF EXISTS match_job_search_database CASCADE;

-- Step 3: Create all functions with correct 768-dimensional vectors

-- <PERSON>'s function (Mental Health)
CREATE FUNCTION match_mental_health_documents(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    mental_health_documents.id,
    mental_health_documents.content,
    mental_health_documents.metadata,
    1 - (mental_health_documents.embedding <=> query_embedding) AS similarity
  FROM mental_health_documents
  WHERE 1 - (mental_health_documents.embedding <=> query_embedding) > match_threshold
  ORDER BY mental_health_documents.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Cody's function (Coding)
CREATE FUNCTION match_coding_database(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    coding_database.id,
    coding_database.content,
    coding_database.metadata,
    1 - (coding_database.embedding <=> query_embedding) AS similarity
  FROM coding_database
  WHERE 1 - (coding_database.embedding <=> query_embedding) > match_threshold
  ORDER BY coding_database.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Scott's function (Job Search)
CREATE FUNCTION match_job_search_database(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    job_search_database.id,
    job_search_database.content,
    job_search_database.metadata,
    1 - (job_search_database.embedding <=> query_embedding) AS similarity
  FROM job_search_database
  WHERE 1 - (job_search_database.embedding <=> query_embedding) > match_threshold
  ORDER BY job_search_database.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Step 4: Grant necessary permissions
GRANT EXECUTE ON FUNCTION match_mental_health_documents TO anon, authenticated;
GRANT EXECUTE ON FUNCTION match_coding_database TO anon, authenticated;
GRANT EXECUTE ON FUNCTION match_job_search_database TO anon, authenticated;
