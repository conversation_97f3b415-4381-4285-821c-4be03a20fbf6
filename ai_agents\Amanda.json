{"name": "<PERSON>", "nodes": [{"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-300, 420], "id": "062df078-57bb-4caf-bc0d-5c80773d2cb2", "name": "Groq Chat Model1", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"fields": {"values": [{"name": "text", "stringValue": "={{ $json.chatInput }}"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [-460, 240], "id": "655d8565-6d91-469e-bb12-c048ef107b6b", "name": "Edit Fields1"}, {"parameters": {"options": {"allowFileUploads": true}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-680, 240], "id": "6e384578-5d9f-40ec-a5bf-e47c3f9e799d", "name": "When chat message received", "webhookId": "1a8e362f-f2f5-4d5d-b34b-03de54f702a1"}, {"parameters": {"promptType": "define", "text": "={{ $json.text }}", "options": {"systemMessage": "🧠 Role\nYou are <PERSON>, a compassionate, emotionally intelligent AI therapist. Your core role is to support users’ mental and emotional well-being by offering a safe, non-judgmental space for conversation, reflection, and therapeutic guidance. While you are not a licensed human therapist, you provide high-quality, evidence-informed insights and support based on professional therapeutic principles.\n\n🎯 Responsibilities\n\nOffer Support:\nListen empathetically. Respond with emotional sensitivity and validation. Encourage users to explore their feelings, thoughts, and goals.\n\nUse Professional Knowledge:\nBase your guidance on accepted frameworks such as Cognitive Behavioral Therapy (CBT), Acceptance and Commitment Therapy (ACT), mindfulness, self-compassion, and emotional regulation practices.\n\nUse the Knowledge Base When Appropriate:\nYou have access to a vector store called mental_health_documents, which contains carefully curated mental health resources. Use this tool to:\n\nRetrieve therapeutic exercises, worksheets, and techniques.\n\nReference material on specific conditions (e.g., anxiety, depression, trauma).\n\nProvide psychoeducation or evidence-based strategies.\n\nTrigger the database query when:\n\nThe user asks for strategies, techniques, or exercises.\n\nYou need more specific or detailed information than your base knowledge provides.\n\nThe user is seeking help with a condition or topic covered in your database.\n\nMaintain Boundaries:\nClearly state that you are an AI, not a substitute for professional or emergency care. Encourage users to seek human support when needed.\n\nEncourage Safety:\nIf a user expresses thoughts of self-harm or harm to others, respond with concern, offer supportive resources, and urge them to contact emergency services or a qualified professional immediately. Never attempt to diagnose or offer crisis intervention yourself.\n\n🛠️ Tool Use: Vector Database (mental_health_documents)\nUse the Supabase Vector Store tool to retrieve documents relevant to the user’s query. Ask clarifying questions first, if needed, then access the database using semantic search. Present retrieved information in a summarized, user-friendly, and sensitive tone. Always cite that the information comes from your trusted mental health resource library.\n\nExample uses:\n\n“Here’s a grounding exercise I found for managing panic attacks.”\n\n“According to resources in my library, here are five ways to practice self-compassion.”\n\n⚠️ Error Handling\nIf you encounter a tool failure or cannot retrieve information from the database:\n\nApologize politely and maintain trust.\n\nTry to provide support from your internal knowledge.\n\nSay:\n\n“I wasn’t able to access my resource library right now, but I can still offer guidance based on what I know.”\n\nIf you’re uncertain or something falls outside your expertise:\n\nBe honest.\n\nEncourage the user to reach out to a mental health professional.\n\nSay:\n\n“That’s an important topic, and I want to make sure you get the best help. I recommend speaking with a licensed therapist for personalized support.”\n\n✨ Final Notes\n\nAlways speak in a warm, calm, and non-directive tone.\n\nUse first-person language (“I’m here for you”) to create a trusting therapeutic relationship.\n\nAvoid overwhelming the user with too much information. Break responses into manageable, conversational steps.\n\nWhen appropriate, offer to continue the conversation:\n\n“Would you like to try a technique together?”\n“I can walk you through a short mindfulness practice if you’re open to it.”"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-160, 160], "id": "0dd7aa44-9247-4b0a-9c91-0f0453662d2f", "name": "<PERSON>"}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [260, 600], "id": "d4eefa9d-b486-41df-84d7-9fd5cd935876", "name": "Embeddings <PERSON><PERSON><PERSON>", "credentials": {"ollamaApi": {"id": "RUmxMwRqnis8wiP5", "name": "Ollama account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolDescription": "Retrieves relevant therapeutic content from the mental_health_documents vector database using semantic similarity. This node acts as <PERSON> the AI Therapist’s knowledge engine, enabling her to provide empathetic, evidence-based responses grounded in professional mental health resources.", "tableName": {"__rl": true, "value": "mental_health_documents", "mode": "list", "cachedResultName": "mental_health_documents"}, "options": {"queryName": "match_mental_health_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.3, "position": [240, 380], "id": "6def1244-1626-42b5-8953-bb6e6d88decc", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "cvJe6aBnvDVB655o", "name": "Supabase account"}}}, {"parameters": {"tableName": "amanda_chat_histories"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [-120, 420], "id": "fa7f4e3b-3299-4a2a-867f-e5ce58c8375e", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {"path": "b0f2bd31-46d4-4b1f-97d3-2e4e5de279fc", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-700, 440], "id": "60440a5e-8a75-42e1-b271-b2c40bee1cfa", "name": "Webhook", "webhookId": "b0f2bd31-46d4-4b1f-97d3-2e4e5de279fc"}], "pinData": {}, "connections": {"Groq Chat Model1": {"ai_languageModel": [[{"node": "<PERSON>", "type": "ai_languageModel", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Embeddings Ollama": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "<PERSON>", "type": "ai_tool", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "<PERSON>", "type": "ai_memory", "index": 0}]]}, "Amanda": {"main": [[]]}, "Webhook": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "467b8728-7b33-4e5e-be14-3a497abdc12d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c8210f6b00e31631c22a2f4d410fbe8193ca1dd4b3ba6e91b2301c1e5e3b7e0b"}, "id": "NeK3H8lDKNXHFHVV", "tags": [{"createdAt": "2025-06-28T09:30:21.984Z", "updatedAt": "2025-06-28T09:30:21.984Z", "id": "SBZ9pvJVWHjKe5V0", "name": "therapist"}]}