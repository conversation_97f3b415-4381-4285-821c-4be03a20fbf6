const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testCodyCodeGenerator() {
  console.log('🔧 Testing Cody\'s Enhanced Code Generation Tool...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Test scenarios for code generation
  const codeGenerationTests = [
    {
      name: 'Pydantic AI Agent for Movie Scripts',
      codeRequest: 'Create a Pydantic AI agent that writes movie scripts based on user prompts',
      language: 'Python',
      requirements: 'use async/await, include error handling, follow clean code principles'
    },
    {
      name: 'React Authentication Component',
      codeRequest: 'Build a React component for user authentication with login and signup',
      language: 'React',
      requirements: 'use hooks, include form validation, handle loading states'
    },
    {
      name: 'Node.js REST API',
      codeRequest: 'Create a REST API endpoint for user management',
      language: 'Node.js',
      requirements: 'use Express, include middleware, add proper error handling'
    },
    {
      name: 'JavaScript Async Patterns',
      codeRequest: 'Implement async/await patterns for API calls',
      language: 'JavaScript',
      requirements: 'include error handling, use try/catch blocks'
    }
  ];

  let allTestsPassed = true;

  for (const test of codeGenerationTests) {
    console.log(`🧪 Testing: ${test.name}`);
    console.log(`   Request: "${test.codeRequest}"`);
    console.log(`   Language: ${test.language}`);
    console.log(`   Requirements: ${test.requirements || 'None'}`);

    try {
      // Step 1: Search for relevant documentation (simulating the tool's first step)
      const searchQueries = [
        `${test.language} ${test.codeRequest}`,
        `${test.language} best practices`,
        `${test.codeRequest} examples`,
        test.requirements ? `${test.language} ${test.requirements}` : `${test.language} patterns`
      ];

      let allRelevantDocs = [];
      
      for (const query of searchQueries) {
        // Generate embedding using Ollama
        const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'nomic-embed-text:latest',
            prompt: query,
          }),
        });

        if (!embeddingResponse.ok) {
          console.log(`      ❌ Failed to generate embedding for query: ${query}`);
          continue;
        }

        const embeddingData = await embeddingResponse.json();
        const queryEmbedding = embeddingData.embedding;

        const { data, error } = await supabase.rpc('match_coding_database', {
          query_embedding: queryEmbedding,
          match_threshold: 0.25, // Slightly lower for broader search
          match_count: 3,
        });

        if (!error && data && data.length > 0) {
          allRelevantDocs.push(...data);
        }
      }

      // Remove duplicates and get top results
      const uniqueDocs = allRelevantDocs
        .filter((doc, index, self) => 
          index === self.findIndex(d => d.id === doc.id)
        )
        .sort((a, b) => (b.similarity || 0) - (a.similarity || 0))
        .slice(0, 8);

      if (uniqueDocs.length === 0) {
        console.log(`      ❌ No relevant documentation found for ${test.name}`);
        allTestsPassed = false;
        continue;
      }

      console.log(`      ✅ Found ${uniqueDocs.length} relevant references`);
      console.log(`      📊 Best match similarity: ${uniqueDocs[0].similarity?.toFixed(3)}`);
      console.log(`      📄 Top reference: "${uniqueDocs[0].content?.substring(0, 100)}..."`);

      // Step 2: Simulate the knowledge context that would be used for code generation
      const knowledgeContext = uniqueDocs
        .map((doc, index) => `[Reference ${index + 1}] ${doc.content.substring(0, 200)}...`)
        .join('\n\n');

      console.log(`      📋 Knowledge context preview:`);
      console.log(`         ${knowledgeContext.substring(0, 300)}...`);

      // Step 3: Show what the tool would return
      console.log(`      🎯 Tool would generate code using:`);
      console.log(`         - ${uniqueDocs.length} knowledge base references`);
      console.log(`         - ${test.language} best practices from documentation`);
      console.log(`         - Patterns and examples from similar implementations`);
      
      if (test.requirements) {
        console.log(`         - Specific requirements: ${test.requirements}`);
      }

    } catch (error) {
      console.log(`      ❌ Test failed: ${error.message}`);
      allTestsPassed = false;
    }

    console.log(''); // Empty line between tests
  }

  // Test the specific Pydantic AI scenario
  console.log('🎯 Special Test: Pydantic AI Movie Script Generator...');
  
  try {
    const pydanticQuery = 'pydantic ai agent movie script generator';
    console.log(`Query: "${pydanticQuery}"`);
    
    const embeddingResponse = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'nomic-embed-text:latest',
        prompt: pydanticQuery,
      }),
    });

    const embeddingData = await embeddingResponse.json();
    const queryEmbedding = embeddingData.embedding;

    const { data, error } = await supabase.rpc('match_coding_database', {
      query_embedding: queryEmbedding,
      match_threshold: 0.25,
      match_count: 5
    });

    if (error) {
      console.log(`❌ Pydantic AI search failed: ${error.message}`);
      allTestsPassed = false;
    } else if (!data || data.length === 0) {
      console.log(`❌ No Pydantic AI documentation found`);
      allTestsPassed = false;
    } else {
      console.log(`✅ Found ${data.length} Pydantic AI references`);
      console.log(`📄 Best match: "${data[0].content?.substring(0, 150)}..."`);
      console.log(`🎯 Similarity: ${data[0].similarity?.toFixed(3)}`);
      
      // Check if it contains relevant Pydantic AI information
      const hasRelevantInfo = data.some(doc => 
        doc.content.toLowerCase().includes('pydantic') && 
        (doc.content.toLowerCase().includes('agent') || doc.content.toLowerCase().includes('ai'))
      );
      
      if (hasRelevantInfo) {
        console.log(`✅ Contains relevant Pydantic AI agent information`);
      } else {
        console.log(`⚠️  Found documents but may not be specifically about Pydantic AI agents`);
      }
    }

  } catch (error) {
    console.log(`❌ Pydantic AI test failed: ${error.message}`);
    allTestsPassed = false;
  }

  // Final summary
  console.log('\n' + '='.repeat(60));
  if (allTestsPassed) {
    console.log('🎉 SUCCESS! Cody\'s Code Generator Tool is Ready!');
    console.log('');
    console.log('✅ Knowledge base search is working');
    console.log('✅ Multiple search queries find relevant documentation');
    console.log('✅ Pydantic AI documentation is accessible');
    console.log('✅ Tool can find patterns and best practices');
    console.log('');
    console.log('🔧 WHAT CODY CAN NOW DO:');
    console.log('• Generate Pydantic AI agents with proper patterns');
    console.log('• Create React components following documented best practices');
    console.log('• Build Node.js APIs using established patterns');
    console.log('• Write JavaScript code with proper async/await usage');
    console.log('• Follow clean code principles from knowledge base');
    console.log('');
    console.log('🚀 NEXT STEPS:');
    console.log('1. Restart your Next.js server to load the new tool');
    console.log('2. Ask Cody to generate code (e.g., "Create a Pydantic AI agent for movie scripts")');
    console.log('3. Cody will use his knowledge base to write clean, accurate code!');
  } else {
    console.log('⚠️  Some issues found. Please check the errors above.');
  }
  console.log('='.repeat(60));
}

testCodyCodeGenerator().catch(console.error);
