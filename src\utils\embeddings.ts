// Embedding utility to ensure consistency across RAG agents
// This matches the Ollama nomic-embed-text model used in n8n

interface EmbeddingResponse {
  embedding: number[];
}

/**
 * Generate embeddings using Ollama API to match n8n agents
 * This ensures consistent 768-dimensional vectors across all agents
 */
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    // Use Ollama API directly to match n8n configuration
    const response = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'nomic-embed-text:latest',
        prompt: text,
      }),
    });

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
    }

    const data: EmbeddingResponse = await response.json();
    
    if (!data.embedding || !Array.isArray(data.embedding)) {
      throw new Error('Invalid embedding response from Ollama');
    }

    // Verify dimensions match expected 768
    if (data.embedding.length !== 768) {
      console.warn(`Warning: Expected 768 dimensions, got ${data.embedding.length}`);
    }

    return data.embedding;
  } catch (error) {
    console.error('Error generating embedding with Ollama:', error);
    
    // Fallback: Try using Google AI if Ollama is not available
    console.log('Falling back to Google AI embeddings...');
    return await generateGoogleEmbedding(text);
  }
}

/**
 * Fallback embedding using Google AI
 * Note: This may produce different dimensions than Ollama
 */
async function generateGoogleEmbedding(text: string): Promise<number[]> {
  try {
    const { ai } = await import('@/ai/genkit');
    const embedding = await ai.embed({
      content: text,
    });
    
    // Convert to array if needed
    if (Array.isArray(embedding)) {
      return embedding;
    }
    
    // Handle different response formats
    if (typeof embedding === 'object' && 'values' in embedding) {
      return (embedding as any).values;
    }
    
    throw new Error('Unexpected embedding format from Google AI');
  } catch (error) {
    console.error('Error generating Google AI embedding:', error);
    throw new Error('Both Ollama and Google AI embedding generation failed');
  }
}

/**
 * Check if Ollama is running and accessible
 */
export async function checkOllamaStatus(): Promise<boolean> {
  try {
    const response = await fetch('http://localhost:11434/api/tags', {
      method: 'GET',
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * Ensure the nomic-embed-text model is available in Ollama
 */
export async function ensureEmbeddingModel(): Promise<boolean> {
  try {
    const response = await fetch('http://localhost:11434/api/tags');
    if (!response.ok) return false;
    
    const data = await response.json();
    const models = data.models || [];
    
    return models.some((model: any) => 
      model.name && model.name.includes('nomic-embed-text')
    );
  } catch (error) {
    return false;
  }
}
