-- Fix Supabase RAG Agent Database Issues
-- Run this SQL in your Supabase SQL Editor

-- 1. Create the missing RPC function for <PERSON> (Job Search Agent)
CREATE OR REPLACE FUNCTION match_job_search_database(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    job_search_database.id,
    job_search_database.content,
    job_search_database.metadata,
    1 - (job_search_database.embedding <=> query_embedding) AS similarity
  FROM job_search_database
  WHERE 1 - (job_search_database.embedding <=> query_embedding) > match_threshold
  ORDER BY job_search_database.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- 2. Verify and potentially recreate the other RPC functions with correct vector dimensions
-- (These should already exist, but let's ensure they're using vector(768))

CREATE OR REPLACE FUNCTION match_mental_health_documents(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    mental_health_documents.id,
    mental_health_documents.content,
    mental_health_documents.metadata,
    1 - (mental_health_documents.embedding <=> query_embedding) AS similarity
  FROM mental_health_documents
  WHERE 1 - (mental_health_documents.embedding <=> query_embedding) > match_threshold
  ORDER BY mental_health_documents.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

CREATE OR REPLACE FUNCTION match_coding_database(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    coding_database.id,
    coding_database.content,
    coding_database.metadata,
    1 - (coding_database.embedding <=> query_embedding) AS similarity
  FROM coding_database
  WHERE 1 - (coding_database.embedding <=> query_embedding) > match_threshold
  ORDER BY coding_database.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- 3. Ensure proper indexes exist for vector similarity search
CREATE INDEX IF NOT EXISTS mental_health_documents_embedding_idx 
ON mental_health_documents USING ivfflat (embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS coding_database_embedding_idx 
ON coding_database USING ivfflat (embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS job_search_database_embedding_idx 
ON job_search_database USING ivfflat (embedding vector_cosine_ops);

-- 4. Grant necessary permissions
GRANT EXECUTE ON FUNCTION match_mental_health_documents TO anon, authenticated;
GRANT EXECUTE ON FUNCTION match_coding_database TO anon, authenticated;
GRANT EXECUTE ON FUNCTION match_job_search_database TO anon, authenticated;

-- 5. Verify table structures (informational queries)
-- You can run these to check your table structures:

-- SELECT column_name, data_type 
-- FROM information_schema.columns 
-- WHERE table_name = 'mental_health_documents';

-- SELECT column_name, data_type 
-- FROM information_schema.columns 
-- WHERE table_name = 'coding_database';

-- SELECT column_name, data_type 
-- FROM information_schema.columns 
-- WHERE table_name = 'job_search_database';
