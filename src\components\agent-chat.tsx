
'use client';

import type { Agent } from '@/lib/types';
import { agentChatAction, loadChatHistoryAction, sendEmailAction, transcribeAudioAction, synthesizeSpeechAction, type ChatActionState, type SendEmailActionState } from '@/app/actions';
import { useActionState, useEffect, useRef, useState, useTransition } from 'react';
import { useFormStatus } from 'react-dom';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { ScrollArea } from './ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Loader2, Mic, Square, Volume2, Image as ImageIcon, Send, ShieldCheck, Pencil } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Alert, AlertTitle, AlertDescription } from './ui/alert';
import { DynamicIcon } from './icons';
import { useToast } from '@/hooks/use-toast';

interface EmailDraft {
  to: string;
  subject: string;
  body: string;
}

function parseStructuredDraft(messageContent: string): Omit<EmailDraft, 'to'> | null {
  const subjectMatch = messageContent.match(/^Subject: (.*)$/m);
  const bodyMatch = messageContent.match(/^Body:\n([\s\S]*)/m);

  if (subjectMatch && bodyMatch) {
    return {
      subject: subjectMatch[1].trim(),
      body: bodyMatch[1].trim(),
    };
  }
  return null;
}

function parseRecipient(messageContent: string): string | null {
    const toMatch = messageContent.match(/^To: (.*)$/m);
    return toMatch ? toMatch[1].trim() : null;
}

function ApproveSendButton() {
    const { pending } = useFormStatus();
    return (
        <Button type="submit" disabled={pending}>
            {pending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <ShieldCheck className="mr-2 h-4 w-4" />}
            Approve & Send
        </Button>
    );
}

export function AgentChat({ agent }: { agent: Agent }) {
  const { toast } = useToast();
  const [sessionId, setSessionId] = useState('');
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);

  // State for the main chat form
  const [chatState, chatFormAction, isPending] = useActionState(agentChatAction, {
    messages: [{ role: 'assistant', content: `Hello! I'm ${agent.name}. How can I assist you today?`}]
  });

  // Local state for messages (to handle history loading)
  const [messages, setMessages] = useState(chatState.messages);

  // State for the email approval form
  const [emailState, emailFormAction] = useActionState<SendEmailActionState, FormData>(sendEmailAction, {});
  const [emailDraft, setEmailDraft] = useState<EmailDraft | null>(null);

  const formRef = useRef<HTMLFormElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [promptValue, setPromptValue] = useState('');
  
  // Audio state
  const [isRecording, setIsRecording] = useState(false);
  const [isTranscribing, startTranscribing] = useTransition();
  const [isPlaying, setIsPlaying] = useState<string | null>(null);
  const [isSynthesizing, startSynthesizing] = useTransition();
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const { pending } = useFormStatus();

  useEffect(() => {
    if (typeof window !== 'undefined' && window.crypto) {
      const newSessionId = crypto.randomUUID();
      setSessionId(newSessionId);

      // Load chat history for this session
      loadChatHistoryAction(agent.id, newSessionId).then((historyState) => {
        setMessages(historyState.messages);
        setIsLoadingHistory(false);
      }).catch((error) => {
        console.error('Failed to load chat history:', error);
        setIsLoadingHistory(false);
      });
    }
    audioRef.current = new Audio();
  }, [agent.id]);

  // Sync messages when chatState changes (from new messages)
  useEffect(() => {
    setMessages(chatState.messages);
  }, [chatState.messages]);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current && !isLoadingHistory) {
        const viewport = scrollAreaRef.current.querySelector('div[data-radix-scroll-area-viewport]');
        if (viewport) {
            viewport.scrollTo({ top: viewport.scrollHeight, behavior: 'smooth' });
        }
    }
  }, [messages, isLoadingHistory]);

  // Handle parsing for email draft when Li responds
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (agent.id === 'x3M1ATR5WY16vkPd' && lastMessage && lastMessage.role === 'assistant') {
      const draft = parseStructuredDraft(lastMessage.content);
      const recipient = parseRecipient(lastMessage.content);
      if (draft && recipient) {
        setEmailDraft({ ...draft, to: recipient });
      } else {
        setEmailDraft(null);
      }
    }
  }, [messages, agent.id]);

  // Handle toast notifications from email sending
  useEffect(() => {
    if (emailState.message) {
      toast({ title: 'Email Sent!', description: emailState.message });
      setEmailDraft(null);
    } else if (emailState.error) {
      toast({ variant: 'destructive', title: 'Email Error', description: emailState.error });
    }
  }, [emailState, toast]);

  const handleRecord = async () => {
    if (isRecording) {
      mediaRecorderRef.current?.stop();
      setIsRecording(false);
      return;
    }
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setIsRecording(true);
      mediaRecorderRef.current = new MediaRecorder(stream);
      const audioChunks: Blob[] = [];
      mediaRecorderRef.current.ondataavailable = (event) => audioChunks.push(event.data);
      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        reader.onloadend = () => {
          const base64data = reader.result as string;
          const formData = new FormData();
          formData.append('audioDataUri', base64data);
          startTranscribing(async () => {
            const result = await transcribeAudioAction(formData);
            if(result.error) toast({ variant: 'destructive', title: 'Transcription Error', description: result.error });
            else if (result.transcription) setPromptValue(result.transcription);
          });
        };
        stream.getTracks().forEach(track => track.stop());
      };
      mediaRecorderRef.current.start();
    } catch (error) {
      console.error('Error accessing microphone:', error);
      toast({ variant: 'destructive', title: 'Microphone Error', description: 'Could not access the microphone.' });
      setIsRecording(false);
    }
  };

  const handlePlayAudio = (text: string, messageId: string) => {
    if (isPlaying === messageId) {
      audioRef.current?.pause();
      setIsPlaying(null);
      return;
    }
    startSynthesizing(async () => {
      setIsPlaying(messageId);
      const formData = new FormData();
      formData.append('text', text);
      const result = await synthesizeSpeechAction(formData);
      if (result.error) {
        toast({ variant: 'destructive', title: 'Audio Error', description: result.error });
        setIsPlaying(null);
      } else if (result.audioDataUri && audioRef.current) {
        audioRef.current.src = result.audioDataUri;
        audioRef.current.play().catch(e => console.error("Playback failed", e));
        audioRef.current.onended = () => setIsPlaying(null);
      }
    });
  }
  
  const submitForm = () => {
    if(!promptValue.trim() || pending) return;
    formRef.current?.requestSubmit();
    setPromptValue('');
  }

  return (
    <div className="flex-1 flex flex-col w-full h-full bg-background rounded-b-xl shadow-lg overflow-hidden">
      <div className="p-4 border-b flex items-center gap-4 shrink-0">
        <Avatar className="h-10 w-10 border">
            <AvatarFallback className="bg-secondary">
              <DynamicIcon name={agent.icon} className="h-6 w-6" />
            </AvatarFallback>
        </Avatar>
        <div>
          <h2 className="text-lg font-semibold">{agent.name}</h2>
          <p className="text-sm text-muted-foreground">{agent.description}</p>
        </div>
      </div>
      <ScrollArea className="flex-1 p-6" ref={scrollAreaRef}>
        <div className="space-y-8">
          {isLoadingHistory ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">Loading conversation history...</span>
            </div>
          ) : (
            messages.map((message, index) => (
            <div key={index} className={cn("flex items-start gap-4", message.role === 'user' ? 'justify-end' : '')}>
              {message.role === 'assistant' && (
                <Avatar className="h-8 w-8 border">
                   <AvatarFallback className="bg-secondary text-foreground">
                    <DynamicIcon name={agent.icon} className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
              )}
              <div className={cn("flex flex-col gap-1 items-start max-w-xl", message.role === 'user' ? 'items-end' : '')}>
                <div className={cn(
                  "p-4 rounded-3xl relative group text-sm leading-6",
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground rounded-br-lg'
                    : 'bg-card border rounded-bl-lg'
                )}>
                  <p className="whitespace-pre-wrap">{message.content}</p>
                   {message.role === 'assistant' && (
                     <Button
                       variant="ghost"
                       size="icon"
                       className="absolute -top-2 -right-2 h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity bg-background border rounded-full"
                       onClick={() => handlePlayAudio(message.content, `msg-${index}`)}
                       disabled={isSynthesizing}
                     >
                        {(isSynthesizing || isPlaying) && isPlaying === `msg-${index}` ? (
                          <Loader2 className="h-4 w-4 animate-spin"/>
                        ) : (
                          <Volume2 className="h-4 w-4" />
                        )}
                        <span className="sr-only">Play audio</span>
                     </Button>
                   )}
                </div>
              </div>
              {message.role === 'user' && (
                <Avatar className="h-8 w-8 border">
                  <AvatarImage src="https://placehold.co/100x100.png" alt="@sophia" data-ai-hint="woman face"/>
                  <AvatarFallback>S</AvatarFallback>
                </Avatar>
              )}
            </div>
            ))
          )}
          {isPending && (
             <div className="flex items-start gap-4">
               <Avatar className="h-8 w-8 border">
                 <AvatarFallback className="bg-secondary text-foreground">
                   <DynamicIcon name={agent.icon} className="h-5 w-5" />
                 </AvatarFallback>
               </Avatar>
                <div className="flex flex-col gap-1 items-start">
                    <div className="p-4 rounded-3xl max-w-sm md:max-w-md bg-card border rounded-bl-lg">
                        <Loader2 className="h-5 w-5 animate-spin" />
                    </div>
                </div>
             </div>
          )}
        </div>
      </ScrollArea>

      {chatState.error && (
          <div className="px-6 pb-4 shrink-0">
              <Alert variant="destructive">
                  <AlertTitle>Chat Error</AlertTitle>
                  <AlertDescription>{chatState.error}</AlertDescription>
              </Alert>
          </div>
      )}

      {emailDraft ? (
          <div className="p-4 border-t bg-card rounded-b-xl shrink-0">
             <form action={emailFormAction}>
                <input type="hidden" name="to" value={emailDraft.to} />
                <input type="hidden" name="subject" value={emailDraft.subject} />
                <input type="hidden" name="body" value={emailDraft.body} />
                <p className="text-sm font-medium text-center mb-3">Does this email draft look correct?</p>
                <div className="flex gap-4 justify-center">
                    <ApproveSendButton />
                    <Button type="button" variant="outline" onClick={() => setEmailDraft(null)}>
                        <Pencil className="mr-2 h-4 w-4" />
                        Edit
                    </Button>
                </div>
             </form>
          </div>
      ) : (
        <div className="p-4 border-t bg-card rounded-b-xl shrink-0">
            <form ref={formRef} action={chatFormAction} className="relative">
                <Textarea
                    id="prompt"
                    name="prompt"
                    placeholder={`Message ${agent.name}...`}
                    className="flex-1 resize-none pr-28 pl-4 py-3 rounded-xl bg-secondary border-none focus-visible:ring-1 focus-visible:ring-ring min-h-[52px]"
                    rows={1}
                    value={promptValue}
                    onChange={(e) => setPromptValue(e.target.value)}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            submitForm();
                        }
                    }}
                    disabled={pending}
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
                    <Button type="button" variant="ghost" size="icon" className="text-muted-foreground">
                        <ImageIcon className="h-5 w-5" />
                        <span className="sr-only">Attach image</span>
                    </Button>
                    <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={handleRecord}
                        disabled={isTranscribing || pending}
                        className="text-muted-foreground"
                    >
                        {isTranscribing ? <Loader2 className="h-5 w-5 animate-spin" /> : (isRecording ? <Square className="h-5 w-5 text-red-500" /> : <Mic className="h-5 w-5" />)}
                        <span className="sr-only">{isRecording ? 'Stop recording' : 'Start recording'}</span>
                    </Button>
                    <Button type="button" size="icon" onClick={submitForm} disabled={!promptValue.trim() || pending}>
                        {pending ? <Loader2 className="h-5 w-5 animate-spin"/> : <Send className="h-5 w-5"/>}
                        <span className="sr-only">Send</span>
                    </Button>
                </div>
                <input type="hidden" name="agentId" value={agent.id} />
                <input type="hidden" name="sessionId" value={sessionId} />
            </form>
        </div>
      )}
    </div>
  );
}
