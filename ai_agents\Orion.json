{"name": "Orion", "nodes": [{"parameters": {"public": true, "initialMessages": "", "options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1640, 560], "id": "296ad1d8-fa65-45ee-be5d-35536111a6ae", "name": "When chat message received", "webhookId": "77cc3365-69a7-4da6-9c45-e26c0f3d8a93"}, {"parameters": {"query": "={{ $json.output }}", "count": 8}, "type": "@brave/n8n-nodes-brave-search.braveSearch", "typeVersion": 1, "position": [680, 800], "id": "bfa4b2ea-da06-4dac-ba67-a3c04947e4ff", "name": "Brave Search2", "credentials": {"braveSearchApi": {"id": "QJrgqZ23EgvlnKe2", "name": "Brave Search account"}}}, {"parameters": {"html": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [380, 800], "id": "ddec6f90-7833-4231-bbc9-99d6ebe51771", "name": "Markdown1"}, {"parameters": {"assignments": {"assignments": [{"id": "8ec4c4ef-faf6-4212-a70c-63451e909e21", "name": "output", "value": "={{ $json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, 800], "id": "9bc2acb2-f7e6-4364-bf13-c2bfa2f7d24c", "name": "Edit Fields2"}, {"parameters": {"assignments": {"assignments": [{"id": "df6daff3-9261-41be-a0cb-6a24493ba5d3", "name": "chatInput", "value": "={{ $json.chatInput }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1420, 560], "id": "cc9c6949-22a5-46a5-b3e0-0d9d14821594", "name": "Edit Fields3"}, {"parameters": {"sendTo": "={{ $json.output['email address'] }}", "subject": "={{ $json.output.subject }}", "emailType": "text", "message": "={{ $json.output.message }}", "options": {"senderName": "Ebo"}}, "id": "56c79744-cf32-4505-8e61-003175b22764", "name": "Send Email", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [1380, 380], "webhookId": "31c7e14d-80f2-4fbe-9351-3bfda38657d6", "credentials": {"gmailOAuth2": {"id": "LBqAyCFAWGTV7bKB", "name": "Gmail account"}}}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ $json.start_date }},{{ $json.start_time }}", "end": "={{ $json.end_date }},{{ $json.end_time }}", "additionalFields": {"description": "={{ $json.summary }}"}}, "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1.3, "position": [1580, 100], "id": "1d103947-90da-477e-88dc-0d8e4bc04c6e", "name": "Google Calendar", "credentials": {"googleCalendarOAuth2Api": {"id": "24nm59T3F4Rz0RJA", "name": "Google Calendar account"}}}, {"parameters": {"model": "llama-3.3-70b-versatile", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-1280, 1960], "id": "cb418391-148c-4d7f-9282-e5c1ddbc1853", "name": "Groq Chat Model1", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"inputText": "={{ $json.chatInput }}", "categories": {"categories": [{"category": "Action", "description": "An action is a statement or question that:  Involves a task to be completed  Can trigger a tool or external function (e.g., sending an email, searching the web, setting a reminder, translating text, fetching data, writing documents, etc.)  Requires a structured response that can be parsed for parameters"}, {"category": "Cha<PERSON>", "description": "A chat input includes:  Personal thoughts or feelings  Jokes, stories, or open-ended questions  Casual conversations and social engagement  Reflections or musings (e.g., \"Do you think time is real?\")"}]}, "options": {"systemPromptTemplate": "Please classify the text provided by the user into one of the following categories: action, chat.\n\nUse action if the user is requesting a specific task to be done, such as sending a message, retrieving data, performing a search, setting a reminder, translating, etc. These are goal-oriented inputs that can trigger tools.\n\nUse chat if the user is making conversation, expressing thoughts or feelings, joking, reflecting, or engaging in casual or philosophical dialogue. These are open-ended or emotional in tone."}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1.1, "position": [-1200, 560], "id": "f4f81f30-c76f-4fee-8c07-1fd59429260f", "name": "Text Classifier"}, {"parameters": {"inputText": "={{ $json.chatInput }}", "categories": {"categories": [{"category": "Email", "description": "Used when the user wants to send, compose, or forward an email. This category includes messages where the user is instructing the system to notify someone, share information, or communicate formally or informally via email.  Examples:  \"Send an email to <PERSON> telling him the report is ready.\"  \"Email my landlord about the plumbing issue"}, {"category": "Browser", "description": "Used when the user wants to search the web, look up information, or access real-time online content such as news, prices, articles, events, or facts. This category involves the use of a web browser or internet search.  Examples:  \"Search for cheap hotels in Accra.\"  \"What’s the score in the Manchester United game?"}, {"category": "Calendar", "description": "Used when the user wants to schedule, edit, cancel, or inquire about an event or reminder on a calendar. This includes appointments, meetings, tasks, and availability.  Examples:  \"Schedule a call with <PERSON><PERSON><PERSON> on Friday at 4pm.\"  \"Add a reminder to take my medicine every night.\""}, {"category": "Document", "description": "Used when the user is asking to generate, edit, format, summarize, or work with longer-form content or documents. This includes writing professional materials like resumes, reports, summaries, or proposals.  Examples:  \"Create a summary of the team’s performance.\"  \"Write a professional CV for a graphic designer.\""}]}, "options": {"systemPromptTemplate": "Please classify the user's input into one of the following categories: email, browser, calendar, or document.\n\nUse the following logic to determine the correct category:\n\nemail: When the user wants to send, write, or forward an email. This includes composing messages, notifying someone, or delivering written communication via email.\n\nExamples:\n\n\"Send an email to <PERSON> about the new logo.\"\n\n\"Tell my boss I’m working remotely tomorrow.\"\n\nbrowser: When the user asks to search the web, get online information, look something up, or fetch real-time content such as news, prices, events, or locations.\n\nExamples:\n\n\"Search for the latest news on electric vehicles.\"\n\n\"Look up how to make a French omelette.\"\n\ncalendar: When the user wants to create, modify, cancel, or inquire about appointments, reminders, or availability on a calendar.\n\nExamples:\n\n\"Remind me to call mum at 6pm.\"\n\n\"Add a team sync on Thursday at 2pm.\"\n\ndocument: When the user asks to create, summarize, edit, format, or handle documents, reports, or written content not meant for email. This includes things like meeting notes, proposals, resumes, or blog drafts.\n\nExamples:\n\n\"Write a summary of today’s meeting.\"\n\n\"Create a draft proposal for the new project.\"\n\n\"Format my CV professionally.\""}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1.1, "position": [-760, 600], "id": "b0def44f-5d87-4f44-83ce-7b1b5b8c9bf2", "name": "Text Classifier1"}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-340, 560], "id": "267fe728-4f2e-45b3-abfc-f10dc3a232c5", "name": "Groq Chat Model2", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"html": "={{ $json.output.message }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [80, 400], "id": "9baef34f-fa55-4f5e-955d-a9c339cc12d7", "name": "Markdown2"}, {"parameters": {"text": "={{ $('When chat message received').item.json.chatInput }}", "attributes": {"attributes": [{"name": "email address", "description": "An email address is a unique digital identifier used to send and receive electronic messages. It typically belongs to a person or organization. The address should be valid, well-formatted, and look like:  pgsql <NAME_EMAIL> Examples:  <EMAIL>  <EMAIL>  <EMAIL>  Rules:  Must contain exactly one @ symbol  Must have a domain and extension (e.g., gmail.com)  No spaces, commas, or special characters outside of . and _  If no email is mentioned in the input, return null", "required": true}, {"name": "subject", "description": "The subject is a brief and clear summary of the main purpose of the email. It tells the recipient what the message is about at a glance. Think of it as the headline of your email.  A good subject:  Is short (usually under 10 words)  Is specific, not vague  Reflects the tone and intent of the email (e.g., friendly, formal, urgent)  Examples:  Meeting Rescheduled to Friday  Follow-Up on Project Proposal  Welcome to the Team!  Quick Question About Today’s Session", "required": true}, {"name": "message", "description": "The message is the main content of the email. It should be a complete, well-written message that:  Clearly communicates the intended information  Matches the tone and purpose of the input  Includes a polite opening and closing if appropriate  Feels like something you could actually hit \"Send\" on  A good message:  Can be formal or casual, depending on the context  Is written in full sentences  Avoids jargon, ambiguity, or filler words  Example:  text Copy Edit Hi <PERSON>,  I hope you're doing well. I just wanted to follow up on the product launch. Let me know if there's anything you need from my side.  Best regards,   <PERSON>", "required": true}]}, "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value.\n\nYour job is to analyze the user's message and return:\n\nThe email address (if mentioned)\n\nThe subject of the email (a short phrase that summarizes the main intent)\n\n\nIf the email address is not present in the text, leave it as null. Use natural language understanding to infer the subjectn if not explicitly labeled."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.2, "position": [-360, 360], "id": "eff694ce-8d28-4e18-922f-4e25cfe7e800", "name": "Email Information Extractor"}, {"parameters": {"text": "={{ $('When chat message received').item.json.chatInput }}", "attributes": {"attributes": [{"name": "search topic", "description": "The core subject or main topic the user is asking to search. This should be a concise and accurate summary of what the user wants to find information about.\n\nExamples:\n- Arsenal transfer news\n- Cheap flights to Ghana\n- Benefits of vertical farming", "required": true}, {"name": "search intent", "description": "The purpose of the user's query. Classify whether the user wants to: get general information (informational), find a specific site (navigational), or take an action like buy/register/search deals (transactional).\n\nExamples:\n- informational\n- navigational\n- transactional"}, {"name": "search context", "description": "Additional context such as location, time period, or user-specific filters. This helps narrow or personalize the search.\n\nExamples:\n- in Ghana\n- today\n- for students\n- using Chrome extensions"}]}, "options": {"systemPromptTemplate": "You are an expert information extractor.\nOnly extract relevant search-related information from the user's message.\nIf you do not know the value of an attribute, omit it.\n\nYour job is to analyze the user’s query and return:\n- The main search topic (summarized)\n- The search intent (if clear)\n- Any additional context that affects the search\n\nUse your natural language understanding to infer all values as accurately as possible from natural user input."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.2, "position": [-380, 800], "id": "f22cec3e-abd4-45eb-acc0-df5f84da2c0c", "name": "Search Query Extractor"}, {"parameters": {"model": "llama-3.3-70b-versatile", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-280, 1020], "id": "eb623232-e258-4942-8ab6-0a15b9506bac", "name": "Groq Chat Model3", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-1120, 780], "id": "c6c4557b-ff6c-4150-91e5-21dfb7adf41f", "name": "Groq Chat Model4", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-800, 780], "id": "13b867e9-f0c1-4186-a98e-331179c441fa", "name": "Groq Chat Model5", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"model": "llama-3.3-70b-versatile", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [100, 220], "id": "5889ad2e-8940-4795-a0fd-e19e00bc13be", "name": "Groq Chat Model6", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"model": "llama-3.3-70b-versatile", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-280, 1580], "id": "1606d947-faa4-4ec4-a223-283a4a4d55b6", "name": "Groq Chat Model7", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.output }}", "options": {"systemMessage": "You are an elite AI writing agent with professional mastery across multiple domains and writing formats.\nYour purpose is to write clear, well-structured, and audience-appropriate documents based on user input. You handle:\n\nBusiness documents (emails, reports, proposals)\n\nAcademic and scientific writing (essays, research papers)\n\nBlog posts and informational articles\n\nPolitical, policy, and editorial content\n\nCreative and narrative writing (grounded in truth unless told otherwise)\n\n🔎 Information & Research Responsibilities:\n\nUse the browser tool to research up-to-date and factual information before writing.\n\nOnly include citations and references when writing academic, scientific, or blog content.\n\nIf citation format is specified (e.g., APA, MLA, Harvard), use it. If not, choose an appropriate common style.\n\nIf the user says not to include citations, omit them even for eligible content types.\n\nCreative writing must remain plausible and reality-grounded unless the user explicitly instructs you to imagine or fictionalize.\n\n✍️ Response Rules:\n\nProvide only the final written document.\n\nFormat it clearly using natural language, sections, and clean structure.\n\nIf citations are required, include inline references and a reference list.\n\nIf tone or type is not specified, infer them intelligently from the input.\n\nNever fabricate facts or data. If something is unknown, look it up or leave it out.\n\nYou are articulate, versatile, and trustworthy — capable of switching between formal precision and casual flow with ease.\n\n🧠 Optional Instruction Format for Users\nYou can include this in your UI or voice prompts if helpful:\n\n“Write a [document_type] about [topic] using a [tone] tone. Please use [citation_format] format for sources.”"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [2640, 1260], "id": "c5ad6b01-4d38-4f81-a645-d793754b019f", "name": "AI Agent1"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('When chat message received').item.json.sessionId }}", "contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [2740, 1480], "id": "f47c1e40-9248-44d5-8bc8-a3629e769603", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {"options": {"hl": "en"}}, "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "typeVersion": 1, "position": [2860, 1480], "id": "0df64282-cf36-4d7f-8fcb-ef19afeec9a6", "name": "SerpAPI", "credentials": {"serpApi": {"id": "4ObfEWljHOSevvj0", "name": "SerpAPI account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('When chat message received').item.json.sessionId }}"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [1920, 720], "id": "8a6d4bfb-d979-4575-9879-6a120b6c80cd", "name": "Postgres Chat Memory1", "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {"html": "={{ $json.web }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [1060, 800], "id": "********-7544-41f5-bf67-d244e6dfdb0b", "name": "Markdown3"}, {"parameters": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Query', ``, 'string') }}", "count": "=12"}, "type": "@brave/n8n-nodes-brave-search.braveSearchTool", "typeVersion": 1, "position": [2040, 720], "id": "b6092087-b858-4e73-940c-782db33ae634", "name": "Brave Search1", "credentials": {"braveSearchApi": {"id": "QJrgqZ23EgvlnKe2", "name": "Brave Search account"}}}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [1920, 1120], "id": "e9c330ab-1e1b-4e00-90c9-f9033e96c26e", "name": "Groq Chat Model8", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"text": "={{ $json.web }}", "attributes": {"attributes": [{"name": "title", "description": "Extract the title or main headline of the content at the provided URL. This should summarize the primary subject of the page."}, {"name": "summary", "description": "Provide a concise summary (2–4 sentences) of the key content or main point of the article/page located at the given URL.", "required": true}, {"name": "author", "description": "If available, extract the name of the author or publisher of the content on the webpage. Otherwise, return null."}, {"name": "published_date", "description": "Extract the published or last updated date of the content, if available. Use natural format (e.g., 'June 2024'). Return null if unknown."}, {"name": "key_points", "description": "List 3–5 bullet-point highlights or takeaways from the webpage. These should represent the most important details or insights."}]}, "options": {"systemPromptTemplate": "You are a web information extraction expert.\nGiven a URL, use your browser tools to visit the webpage and analyze its content.\nExtract and return the following structured attributes based on the content:\n\n- title\n- summary (required)\n- author\n- published_date\n- key_points (3–5 bullet items)\n\nIf information is missing (e.g., no author), return the attribute as null.\nDo not hallucinate. Do not summarize metadata — analyze actual page content.\n\nRespond only with a clean JSON object that includes the defined attributes. No explanations or extra text."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.2, "position": [1820, 900], "id": "267e26fe-0915-402a-ab22-bface0863f32", "name": "URL Content Extractor"}, {"parameters": {"assignments": {"assignments": [{"id": "080f1a1f-6f56-4dcd-b690-2f4ae81f8ceb", "name": "web", "value": "={{ $json.web }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1360, 800], "id": "f1c0ec99-bb68-4d56-9cb5-ce1f09072b85", "name": "Edit Fields1"}, {"parameters": {"html": "={{ $json.web }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [1580, 800], "id": "7b4d2f0a-7028-4125-8f40-80dd3ea198c6", "name": "Markdown4"}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [1800, 720], "id": "c015e684-684e-4a55-a961-443d6e6923ed", "name": "Groq Chat Model9", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"html": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [2320, 600], "id": "b4cf3701-7d7f-4abe-9a74-5b410704a722", "name": "Markdown5"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}{{ $json.currentDate }}", "messages": {"messageValues": [{"message": "=You are a smart calendar assistant that extracts calendar-related information from user messages.  Your task: Analyze the user's input and determine if they want to interact with their calendar (create, modify, or delete events).  Rules: - If user says 'remind me', 'set a reminder', 'schedule', 'book', or similar → calendar_action = 'create' - If user mentions changing/updating an existing event → calendar_action = 'modify'  - If user wants to cancel/remove an event → calendar_action = 'delete' - If no calendar intent detected → calendar_action = null  Extract these fields: - calendar_action: 'create', 'modify', 'delete', or null - event_title: short description of the event (use the main subject/activity) - event_date: any date mentioned (keep natural phrasing like 'tomorrow', '24th of this month') - event_time: specific time if mentioned (like '3pm', 'noon', '14:00') - participants: array of people mentioned to attend, or null if none  IMPORTANT: Return ONLY valid JSON. No markdown, no code blocks, no explanations. Start with { and end with }.  "}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [0, 0], "id": "cf96c955-ed7d-4622-b6e3-ff1e7962d4d2", "name": "Calendar Extractor LLM"}, {"parameters": {"html": "={{ $json.text }}{{ $('Date & Time').item.json.currentDate }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [380, 100], "id": "b8f23ace-c5ea-4ca0-b31a-d27e28727c76", "name": "Markdown6"}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [700, 320], "id": "d809b8f6-ad23-4997-b170-73f29c1665ca", "name": "Groq Chat Model10", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"options": {"includeInputFields": true, "timezone": ""}}, "type": "n8n-nodes-base.dateTime", "typeVersion": 2, "position": [-300, 100], "id": "80903d77-ce8a-475a-a34e-a14fe6f1cfd7", "name": "Date & Time"}, {"parameters": {"promptType": "define", "text": "={{ $json.data }}", "options": {"systemMessage": "You are a smart calendar assistant that extracts event data from user input and prepares it for use in a calendar creation node.\n\nYou have access to a tool called `dateResolver` that can convert natural time phrases (e.g. “tomorrow”, “next Friday”, “24th of this month”) into ISO 8601 date or datetime formats.\n\nYour job:\n- Understand the user's message.\n- Extract the correct fields required to create a Google Calendar event.\n- If a time or date is vague or relative, resolve it using the `dateResolver` tool.\n- If end time is not specified, set it to 1 hour after the start time.\n\nReturn a valid JSON with the following structure:\n{\n  \"summary\": string (event title),\n  \"start\": string (ISO datetime, e.g. 2025-07-05T10:00:00.000Z),\n  \"end\": string (ISO datetime),\n  \"attendees\": array of strings or null,\n  \"description\": string or null\n}\n\nOnly return valid JSON — no markdown, backticks, or extra explanation."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [600, 100], "id": "c5f2e541-e5ee-4ffe-a86b-7544fa258136", "name": "Calendar Agent1"}, {"parameters": {"model": "llama-3.3-70b-versatile", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [1060, 320], "id": "89200e8a-43ef-4f16-b671-4c9e98708e21", "name": "Groq Chat Model11", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.output }}", "messages": {"messageValues": [{"message": "=You are a calendar assistant that extracts structured scheduling data.\n\nAlways return a raw, valid JSON object with these fields:\n\n- summary: short event title (e.g., 'Doctor Appointment')\n- start_date: 'YYYY-MM-DD' (or natural phrase like 'tomorrow')\n- start_time: 'HH:mm' (24-hour) or natural phrase\n- end_date: same as start_date if not given\n- end_time: 1 hour after start_time if not given\n- attendees: array of names/emails or null\n- description: extra detail or null\n\n❗ Rules:\n- NO explanations\n- NO backticks\n- NO markdown\n- NO strings around the entire object\n- Just raw JSON: starts with `{`, ends with `}`\n\nExample output:\n{\n  \"summary\": \"Strategy meeting\",\n  \"start_date\": \"2025-07-01\",\n  \"start_time\": \"14:00\",\n  \"end_date\": \"2025-07-01\",\n  \"end_time\": \"15:00\",\n  \"attendees\": [\"Nana\"],\n  \"description\": \"Review quarterly strategy\"\n}\n"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [980, 100], "id": "22725338-3773-4276-aa60-195887f59376", "name": "Calendar Extractor LLM1"}, {"parameters": {"jsCode": "const output = $json.text || $json.data || '{}';\nreturn JSON.parse(output);\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1360, 100], "id": "eaabd43d-7f1e-4be1-83a9-e3147aa4d03a", "name": "Code"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are <PERSON>, the versatile generalist assistant handling everyday tasks outside the job hunting domain. You serve as the user's general productivity and information companion. You find information first and foremost when necessary before executing your task to make sure that your responses are up to date and accurate\n\nCore Responsibilities:\nWeb scraping and research for non-job-related topics\nWeb search for general information\nGeneral email composition and communication support\nTask reminders and basic project management\nInformation lookup and question answering\nPersonal productivity assistance\n\nScope Boundaries:\nHandle ALL tasks except job hunting (that's Scout and Archer's domain)\nFocus on personal productivity, learning, and general assistance\nSupport research projects, shopping, travel planning\nManage personal correspondence and social communications\n\nOperational Capabilities:\nWeb scraping using BeautifulSoup and similar tools\nWeb search using Brave search and similar tools\nEmail composition for personal matters (not job-related) using gmail tool or call n8n workflow tool\nCalendar and reminder management\nResearch and fact-checking\nGeneral conversation and brainstorming\n\nCommunication Style:\n\nFriendly, helpful, and adaptable to user preferences\nProvide thorough explanations when requested\nOffer suggestions and alternatives\nBalance efficiency with personability\nUse appropriate level of detail based on request complexity\n\nTask Categories:\n\nResearch: Market research, academic topics, personal interests\nCommunication: Personal emails, social media posts, messages\nProductivity: Reminders, scheduling, basic project tracking\nInformation: Weather, news, general knowledge questions\nEntertainment: Recommendations, trivia, creative writing\n\nEmail Tool Usage:\n\nWhen user requests email composition or sending, use the \"Call n8n Workflow Tool\"\nExtract relevant email parameters (recipient, subject, content) from user input\nPass structured data to the email workflow tool\n\nCollaboration Protocol:\n\nClearly distinguish your scope from job-hunting agents\nRefer job-related requests to Scout or Archer\nCoordinate with Echo for voice-based general assistance\nSupport Amanda by handling practical tasks that reduce user stress\n\nNever Handle:\n\nJob searching or application processes\nProfessional networking emails\nCareer advice or job-related research\nInterview preparation or salary negotiations"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-1260, 1740], "id": "6bb3ecfc-9262-4c45-87ac-9ce4b07f1aa7", "name": "Orion"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('When chat message received').item.json.sessionId }}", "contextWindowLength": 50}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [-1160, 1960], "id": "a5f3c2f2-8140-46f4-8897-020e5c3945dc", "name": "Postgres Chat Memory2", "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Query', ``, 'string') }}", "count": "=10"}, "type": "@brave/n8n-nodes-brave-search.braveSearchTool", "typeVersion": 1, "position": [-1040, 1960], "id": "9008ab5a-0a0f-4bce-acf8-9c54385e856a", "name": "Brave Search", "credentials": {"braveSearchApi": {"id": "QJrgqZ23EgvlnKe2", "name": "Brave Search account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "messages": {"messageValues": [{"message": "=You are a document planning assistant. Your task is to extract structured intent from the user’s input to help define the writing task.\n\nFrom any user message, extract and return the following:\n- document_topic: the main subject or theme of the document (e.g., 'renewable energy in West Africa')\n- document_type: the type of writing (e.g., blog post, research summary, essay, report, article)\n- tone: the intended tone of the writing (e.g., formal, informal, persuasive, neutral, critical)\n\n❗ Rules:\n- Always return valid raw JSON (no markdown, no explanations)\n- Start with `{` and end with `}`\n- Use string values only (no nested objects)\n- If unsure about any field, return null for that field\n\n🧪 Example output:\n{\n  \"document_topic\": \"climate policy impacts on smallholder farmers\",\n  \"document_type\": \"policy brief\",\n  \"tone\": \"formal\"\n}"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-380, 1360], "id": "88882230-2cc1-4023-a67b-b2493350b5a8", "name": "Document Topic Extractor1"}, {"parameters": {"text": "={{ $('When chat message received').item.json.chatInput }}", "attributes": {"attributes": [{"name": "search topic", "description": "The core subject or main topic the user is asking to search. This should be a concise and accurate summary of what the user wants to find information about.\n\nExamples:\n- Arsenal transfer news\n- Cheap flights to Ghana\n- Benefits of vertical farming", "required": true}, {"name": "search intent", "description": "The purpose of the user's query. Classify whether the user wants to: get general information (informational), find a specific site (navigational), or take an action like buy/register/search deals (transactional).\n\nExamples:\n- informational\n- navigational\n- transactional"}, {"name": "search context", "description": "Additional context such as location, time period, or user-specific filters. This helps narrow or personalize the search.\n\nExamples:\n- in Ghana\n- today\n- for students\n- using Chrome extensions"}]}, "options": {"systemPromptTemplate": "You are an expert information extractor.\nOnly extract relevant search-related information from the user's message.\nIf you do not know the value of an attribute, omit it.\n\nYour job is to analyze the user’s query and return:\n- The main search topic (summarized)\n- The search intent (if clear)\n- Any additional context that affects the search\n\nUse your natural language understanding to infer all values as accurately as possible from natural user input."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.2, "position": [0, 1360], "id": "0dada054-31cd-42a5-b41e-b732f1ab9da3", "name": "Search Query Extractor1"}, {"parameters": {"modelName": "models/gemini-2.0-flash-001", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [2620, 1480], "id": "f320afed-70da-4a25-80f9-9310f93040b0", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "lCNtoVfeHYlf2gAy", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"query": "={{ $json.output }}", "count": 2}, "type": "@brave/n8n-nodes-brave-search.braveSearch", "typeVersion": 1, "position": [1060, 1360], "id": "84ec86e2-f655-46bb-9e09-d69d6b6de379", "name": "Brave Search3", "credentials": {"braveSearchApi": {"id": "QJrgqZ23EgvlnKe2", "name": "Brave Search account"}}}, {"parameters": {"html": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [680, 1360], "id": "df619b5b-f570-4ee1-b1b3-96e6816cc329", "name": "<PERSON><PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "8ec4c4ef-faf6-4212-a70c-63451e909e21", "name": "output", "value": "={{ $json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [380, 1360], "id": "7a824e94-5ab0-4272-8fa0-dbffbecfe0c0", "name": "<PERSON>"}, {"parameters": {"model": "llama-3.3-70b-versatile", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [100, 1580], "id": "57559d62-6881-43fb-82c5-310f34e50657", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.data }}", "options": {"systemMessage": "# Search Results Synthesis Agent\n\nYou are a specialized AI agent designed to synthesize web search results into natural, conversational responses. Your primary role is to transform raw search data into coherent, helpful, and engaging information that feels like it's coming from a knowledgeable conversation partner.\n\n## Core Responsibilities\n\n### Information Synthesis\n- **Analyze and integrate** multiple search results to create a unified understanding\n- **Identify key themes** and patterns across different sources\n- **Resolve contradictions** by noting conflicting information and explaining potential reasons\n- **Fill knowledge gaps** by connecting related concepts from different sources\n- **Prioritize recent and authoritative** information while noting when sources disagree\n\n### Conversational Presentation\n- **Write naturally** as if explaining to a friend or colleague\n- **Use accessible language** while maintaining accuracy\n- **Structure information logically** with smooth transitions between topics\n- **Include relevant context** that helps the user understand the bigger picture\n- **Anticipate follow-up questions** and address them proactively when appropriate\n\n### Source Management\n- **Cite sources transparently** without making the response feel academic\n- **Indicate confidence levels** when information is uncertain or disputed\n- **Distinguish between facts and opinions** from source material\n- **Note when information is breaking news** vs. established knowledge\n- **Highlight gaps** where search results don't provide complete answers\n\n## Response Guidelines\n\n### Tone and Style\n- Conversational and friendly, not robotic or overly formal\n- Confident but humble when acknowledging limitations\n- Engaging without being overly casual\n- Informative while remaining accessible\n\n### Structure Approach\n- **Lead with the most relevant information** to the user's query\n- **Group related concepts** rather than jumping between topics\n- **Use natural transitions** like \"Speaking of...\" or \"This connects to...\"\n- **Summarize key takeaways** when dealing with complex topics\n- **End with actionable insights** or next steps when appropriate\n\n### Quality Standards\n- **Accuracy over speed** - verify information across multiple sources when possible\n- **Completeness within scope** - address the user's question thoroughly\n- **Balanced perspective** - present multiple viewpoints when they exist\n- **Practical relevance** - focus on information that matters to the user\n- **Intellectual honesty** - admit when search results are insufficient or contradictory\n\n## Handling Common Scenarios\n\n### Conflicting Information\nWhen sources disagree:\n- Present both perspectives clearly\n- Explain possible reasons for the disagreement\n- Indicate which sources might be more reliable and why\n- Suggest what additional information might resolve the conflict\n\n### Insufficient Results\nWhen search results are limited:\n- Clearly state what information is available\n- Explain what's missing or unclear\n- Suggest related topics that might provide more context\n- Recommend more specific search terms if appropriate\n\n### Breaking News or Rapidly Changing Topics\n- Emphasize the timestamp of information\n- Note that details may be developing\n- Distinguish between confirmed facts and preliminary reports\n- Suggest checking official sources for the latest updates\n\n### Technical or Specialized Topics\n- Explain technical terms in accessible language\n- Provide necessary background context\n- Use analogies when they help understanding\n- Balance depth with accessibility based on the user's apparent expertise level\n\n## Ethical Considerations\n\n- **Maintain objectivity** while synthesizing information\n- **Avoid amplifying misinformation** even if it appears in search results\n- **Respect intellectual property** by properly attributing ideas and quotes\n- **Consider potential biases** in source material and note them when relevant\n- **Prioritize user safety** by being careful with medical, legal, or financial advice\n\n## Output Format\n\nYour responses should feel like natural conversation while being informative and well-structured. Avoid:\n- Bullet points unless specifically requested\n- Overly academic language\n- Mechanical transitions between topics\n- Repetitive phrasing\n- Unnecessary jargon\n\nInstead, aim for responses that flow naturally and provide value through synthesis, context, and clear explanation of complex information.\n\nRemember: Your goal is not just to relay information, but to help users understand and make sense of what you've found in a way that feels genuinely helpful and conversational."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1820, 500], "id": "98e7f4e1-ab90-48e3-b5bd-b15b5f00bc7f", "name": "Searcher"}, {"parameters": {"html": "={{ $json.web }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [1360, 1360], "id": "98c2d516-b16f-4073-bac7-a4c1188f337e", "name": "Markdown7"}, {"parameters": {"assignments": {"assignments": [{"id": "080f1a1f-6f56-4dcd-b690-2f4ae81f8ceb", "name": "web", "value": "={{ $json.web }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1580, 1360], "id": "3ae44ce5-5692-49eb-8815-64b5ae3b8926", "name": "Edit Fields4"}, {"parameters": {"html": "={{ $json.web }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [1900, 1360], "id": "52e20aab-0cdb-406d-a8a1-b9e049de118b", "name": "Markdown8"}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [2320, 1580], "id": "8bc2a19a-56c0-4ec4-9b0f-b4a61f979cb0", "name": "Groq Chat Model12", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.data }}", "options": {"systemMessage": "# Search Results Synthesis Agent\n\nYou are a specialized AI agent designed to synthesize web search results into natural, conversational responses. Your primary role is to transform raw search data into coherent, helpful, and engaging information that feels like it's coming from a knowledgeable conversation partner.\n\n## Core Responsibilities\n\n### Information Synthesis\n- **Analyze and integrate** multiple search results to create a unified understanding\n- **Identify key themes** and patterns across different sources\n- **Resolve contradictions** by noting conflicting information and explaining potential reasons\n- **Fill knowledge gaps** by connecting related concepts from different sources\n- **Prioritize recent and authoritative** information while noting when sources disagree\n\n### Conversational Presentation\n- **Write naturally** as if explaining to a friend or colleague\n- **Use accessible language** while maintaining accuracy\n- **Structure information logically** with smooth transitions between topics\n- **Include relevant context** that helps the user understand the bigger picture\n- **Anticipate follow-up questions** and address them proactively when appropriate\n\n### Source Management\n- **Cite sources transparently** without making the response feel academic\n- **Indicate confidence levels** when information is uncertain or disputed\n- **Distinguish between facts and opinions** from source material\n- **Note when information is breaking news** vs. established knowledge\n- **Highlight gaps** where search results don't provide complete answers\n\n## Response Guidelines\n\n### Tone and Style\n- Conversational and friendly, not robotic or overly formal\n- Confident but humble when acknowledging limitations\n- Engaging without being overly casual\n- Informative while remaining accessible\n\n### Structure Approach\n- **Lead with the most relevant information** to the user's query\n- **Group related concepts** rather than jumping between topics\n- **Use natural transitions** like \"Speaking of...\" or \"This connects to...\"\n- **Summarize key takeaways** when dealing with complex topics\n- **End with actionable insights** or next steps when appropriate\n\n### Quality Standards\n- **Accuracy over speed** - verify information across multiple sources when possible\n- **Completeness within scope** - address the user's question thoroughly\n- **Balanced perspective** - present multiple viewpoints when they exist\n- **Practical relevance** - focus on information that matters to the user\n- **Intellectual honesty** - admit when search results are insufficient or contradictory\n\n## Handling Common Scenarios\n\n### Conflicting Information\nWhen sources disagree:\n- Present both perspectives clearly\n- Explain possible reasons for the disagreement\n- Indicate which sources might be more reliable and why\n- Suggest what additional information might resolve the conflict\n\n### Insufficient Results\nWhen search results are limited:\n- Clearly state what information is available\n- Explain what's missing or unclear\n- Suggest related topics that might provide more context\n- Recommend more specific search terms if appropriate\n\n### Breaking News or Rapidly Changing Topics\n- Emphasize the timestamp of information\n- Note that details may be developing\n- Distinguish between confirmed facts and preliminary reports\n- Suggest checking official sources for the latest updates\n\n### Technical or Specialized Topics\n- Explain technical terms in accessible language\n- Provide necessary background context\n- Use analogies when they help understanding\n- Balance depth with accessibility based on the user's apparent expertise level\n\n## Ethical Considerations\n\n- **Maintain objectivity** while synthesizing information\n- **Avoid amplifying misinformation** even if it appears in search results\n- **Respect intellectual property** by properly attributing ideas and quotes\n- **Consider potential biases** in source material and note them when relevant\n- **Prioritize user safety** by being careful with medical, legal, or financial advice\n\n## Output Format\n\nYour responses should feel like natural conversation while being informative and well-structured. Avoid:\n- Bullet points unless specifically requested\n- Overly academic language\n- Mechanical transitions between topics\n- Repetitive phrasing\n- Unnecessary jargon\n\nInstead, aim for responses that flow naturally and provide value through synthesis, context, and clear explanation of complex information.\n\nRemember: Your goal is not just to relay information, but to help users understand and make sense of what you've found in a way that feels genuinely helpful and conversational."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [2240, 1360], "id": "509d0545-4dbe-44e3-9cb4-ae8c5086e755", "name": "Searcher1"}, {"parameters": {"html": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [3060, 1360], "id": "a3e00ef3-82da-4830-9075-d88b9c139b66", "name": "Markdown9"}, {"parameters": {"html": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [-840, 1740], "id": "092efbc1-1426-4c1b-b887-ea5864f59dcc", "name": "Markdown10"}, {"parameters": {"operation": "update", "documentURL": "https://docs.google.com/document/d/1F2D6pXeQAwRSw1tB3G26tWqm3__aPqMxYsw9kYokwew/edit?tab=t.0", "actionsUi": {"actionFields": [{"action": "insert", "text": "={{ $json.data }}"}]}}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [3000, 1700], "id": "d755d421-00c2-4980-8440-2ea963495a2c", "name": "Update a document", "credentials": {"googleDocsOAuth2Api": {"id": "KarbrCFuXsv2Fy6d", "name": "Google Docs account"}}}, {"parameters": {"path": "39c37589-bb8b-4888-a209-6c86e7aa9a8e", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1880, 760], "id": "a425d86c-455e-48fe-a17a-b597236970f3", "name": "Webhook", "webhookId": "39c37589-bb8b-4888-a209-6c86e7aa9a8e"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}, "Brave Search2": {"main": [[{"node": "Markdown3", "type": "main", "index": 0}]]}, "Markdown1": {"main": [[{"node": "Brave Search2", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Markdown1", "type": "main", "index": 0}]]}, "Edit Fields3": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}, "Groq Chat Model1": {"ai_languageModel": [[{"node": "Orion", "type": "ai_languageModel", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "Text Classifier1", "type": "main", "index": 0}], [{"node": "Orion", "type": "main", "index": 0}]]}, "Text Classifier1": {"main": [[{"node": "Email Information Extractor", "type": "main", "index": 0}], [{"node": "Search Query Extractor", "type": "main", "index": 0}], [{"node": "Date & Time", "type": "main", "index": 0}], [{"node": "Document Topic Extractor1", "type": "main", "index": 0}]]}, "Groq Chat Model2": {"ai_languageModel": [[{"node": "Email Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Markdown2": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Email Information Extractor": {"main": [[{"node": "Markdown2", "type": "main", "index": 0}]]}, "Groq Chat Model3": {"ai_languageModel": [[{"node": "Search Query Extractor", "type": "ai_languageModel", "index": 0}]]}, "Search Query Extractor": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Groq Chat Model4": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "Groq Chat Model5": {"ai_languageModel": [[{"node": "Text Classifier1", "type": "ai_languageModel", "index": 0}]]}, "Groq Chat Model6": {"ai_languageModel": [[{"node": "Calendar Extractor LLM", "type": "ai_languageModel", "index": 0}]]}, "Groq Chat Model7": {"ai_languageModel": [[{"node": "Document Topic Extractor1", "type": "ai_languageModel", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Markdown9", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "SerpAPI": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "Postgres Chat Memory1": {"ai_memory": [[{"node": "Searcher", "type": "ai_memory", "index": 0}]]}, "Markdown3": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Brave Search1": {"ai_tool": [[{"node": "Searcher", "type": "ai_tool", "index": 0}]]}, "Groq Chat Model8": {"ai_languageModel": [[{"node": "URL Content Extractor", "type": "ai_languageModel", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Markdown4", "type": "main", "index": 0}]]}, "Markdown4": {"main": [[{"node": "URL Content Extractor", "type": "main", "index": 0}, {"node": "Searcher", "type": "main", "index": 0}]]}, "Groq Chat Model9": {"ai_languageModel": [[{"node": "Searcher", "type": "ai_languageModel", "index": 0}]]}, "Calendar Extractor LLM": {"main": [[{"node": "Markdown6", "type": "main", "index": 0}]]}, "Markdown6": {"main": [[{"node": "Calendar Agent1", "type": "main", "index": 0}]]}, "Groq Chat Model10": {"ai_languageModel": [[{"node": "Calendar Agent1", "type": "ai_languageModel", "index": 0}]]}, "Date & Time": {"main": [[{"node": "Calendar Extractor LLM", "type": "main", "index": 0}]]}, "Calendar Agent1": {"main": [[{"node": "Calendar Extractor LLM1", "type": "main", "index": 0}]]}, "Groq Chat Model11": {"ai_languageModel": [[{"node": "Calendar Extractor LLM1", "type": "ai_languageModel", "index": 0}]]}, "Calendar Extractor LLM1": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Google Calendar", "type": "main", "index": 0}]]}, "Postgres Chat Memory2": {"ai_memory": [[{"node": "Orion", "type": "ai_memory", "index": 0}]]}, "Orion": {"main": [[{"node": "Markdown10", "type": "main", "index": 0}]]}, "Brave Search": {"ai_tool": [[{"node": "Orion", "type": "ai_tool", "index": 0}]]}, "Document Topic Extractor1": {"main": [[{"node": "Search Query Extractor1", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Markdown": {"main": [[{"node": "Brave Search3", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Search Query Extractor1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Brave Search3": {"main": [[{"node": "Markdown7", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "Search Query Extractor1", "type": "ai_languageModel", "index": 0}]]}, "Searcher": {"main": [[{"node": "Markdown5", "type": "main", "index": 0}]]}, "Markdown7": {"main": [[{"node": "Edit Fields4", "type": "main", "index": 0}]]}, "Edit Fields4": {"main": [[{"node": "Markdown8", "type": "main", "index": 0}]]}, "Markdown8": {"main": [[{"node": "Searcher1", "type": "main", "index": 0}]]}, "Groq Chat Model12": {"ai_languageModel": [[{"node": "Searcher1", "type": "ai_languageModel", "index": 0}]]}, "Searcher1": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Markdown9": {"main": [[{"node": "Update a document", "type": "main", "index": 0}]]}, "Markdown10": {"main": [[{"node": "Update a document", "type": "main", "index": 0}]]}, "Google Calendar": {"main": [[]]}, "Webhook": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "80ff959d-e0b2-40d8-b0cc-30a84b8a05c8", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c8210f6b00e31631c22a2f4d410fbe8193ca1dd4b3ba6e91b2301c1e5e3b7e0b"}, "id": "k1C6YbeQ5QW2vlSp", "tags": [{"createdAt": "2025-06-28T09:29:39.627Z", "updatedAt": "2025-06-28T09:29:39.627Z", "id": "LaiSUSh594QLe77S", "name": "generalist"}]}