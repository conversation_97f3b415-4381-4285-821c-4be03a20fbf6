const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function checkTableStructure() {
  console.log('🔍 Checking Current Chat Table Structure...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  const tables = [
    'amanda_chat_histories',
    'cody_chat_histories', 
    'scout_chat_histories',
    'rob_chat_histories',
    'n8n_chat_histories'
  ];

  for (const tableName of tables) {
    console.log(`📋 Table: ${tableName}`);
    
    try {
      // Get table structure by querying information_schema
      const { data, error } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', tableName)
        .order('ordinal_position');

      if (error) {
        console.log(`   ❌ Error: ${error.message}`);
      } else if (data && data.length > 0) {
        console.log(`   ✅ Columns found:`);
        data.forEach(col => {
          console.log(`      - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
        });
      } else {
        console.log(`   ⚠️  No columns found or table doesn't exist`);
      }
    } catch (error) {
      console.log(`   ❌ Query failed: ${error.message}`);
    }
    
    console.log('');
  }

  console.log('🔧 DIAGNOSIS:');
  console.log('The tables exist but may have different column names or structure.');
  console.log('You need to run the setup-chat-memory-tables.sql script in Supabase.');
  console.log('This will create the tables with the correct schema or update existing ones.');
}

checkTableStructure().catch(console.error);
