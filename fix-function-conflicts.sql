-- Fix Function Conflicts - Handle Non-Unique Function Names
-- This script uses a more robust approach to handle existing functions

-- Step 1: Use dynamic SQL to drop all versions of conflicting functions
DO $$
DECLARE
    func_record RECORD;
    drop_statement TEXT;
BEGIN
    -- Handle match_mental_health_documents
    FOR func_record IN 
        SELECT 
            p.oid,
            p.proname,
            pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p
        WHERE p.proname = 'match_mental_health_documents'
    LOOP
        drop_statement := 'DROP FUNCTION IF EXISTS ' || func_record.proname || '(' || func_record.args || ')';
        RAISE NOTICE 'Executing: %', drop_statement;
        EXECUTE drop_statement;
    END LOOP;
    
    -- Handle match_coding_database
    FOR func_record IN 
        SELECT 
            p.oid,
            p.proname,
            pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p
        WHERE p.proname = 'match_coding_database'
    LOOP
        drop_statement := 'DROP FUNCTION IF EXISTS ' || func_record.proname || '(' || func_record.args || ')';
        RAISE NOTICE 'Executing: %', drop_statement;
        EXECUTE drop_statement;
    END LOOP;
    
    -- Handle match_job_search_database
    FOR func_record IN 
        SELECT 
            p.oid,
            p.proname,
            pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p
        WHERE p.proname = 'match_job_search_database'
    LOOP
        drop_statement := 'DROP FUNCTION IF EXISTS ' || func_record.proname || '(' || func_record.args || ')';
        RAISE NOTICE 'Executing: %', drop_statement;
        EXECUTE drop_statement;
    END LOOP;
    
    RAISE NOTICE 'All conflicting functions have been dropped.';
END $$;

-- Step 2: Create the functions with correct signatures

-- Amanda's function (Mental Health)
CREATE FUNCTION match_mental_health_documents(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    mental_health_documents.id,
    mental_health_documents.content,
    mental_health_documents.metadata,
    1 - (mental_health_documents.embedding <=> query_embedding) AS similarity
  FROM mental_health_documents
  WHERE 1 - (mental_health_documents.embedding <=> query_embedding) > match_threshold
  ORDER BY mental_health_documents.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Cody's function (Coding)
CREATE FUNCTION match_coding_database(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    coding_database.id,
    coding_database.content,
    coding_database.metadata,
    1 - (coding_database.embedding <=> query_embedding) AS similarity
  FROM coding_database
  WHERE 1 - (coding_database.embedding <=> query_embedding) > match_threshold
  ORDER BY coding_database.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Scott's function (Job Search)
CREATE FUNCTION match_job_search_database(
  query_embedding vector(768),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    job_search_database.id,
    job_search_database.content,
    job_search_database.metadata,
    1 - (job_search_database.embedding <=> query_embedding) AS similarity
  FROM job_search_database
  WHERE 1 - (job_search_database.embedding <=> query_embedding) > match_threshold
  ORDER BY job_search_database.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Step 3: Grant permissions
GRANT EXECUTE ON FUNCTION match_mental_health_documents TO anon, authenticated;
GRANT EXECUTE ON FUNCTION match_coding_database TO anon, authenticated;
GRANT EXECUTE ON FUNCTION match_job_search_database TO anon, authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ All RAG agent functions have been successfully created with 768-dimensional vector support!';
END $$;
