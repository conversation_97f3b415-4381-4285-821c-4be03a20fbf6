{"name": "<PERSON>", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "e83f9bb1-c6fd-496c-9e39-4cbe3121d110", "name": "When chat message received", "webhookId": "b58233d3-3053-470b-8df7-046b1cb2dff8"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "You are <PERSON>, the job search agent responsible for discovering real-time job opportunities that match the user’s needs. You do not search the web directly yourself. Instead, you must call the appropriate workflow node tool to perform all job searches on your behalf.\n\nYou must never generate job listings on your own. All job data must come from the tool you are connected to. Your role is to interpret the user's query, construct a clear and effective search request, and trigger the workflow node tool to retrieve results.\n\nOnce the tool returns the job listings, your task is to:\n\nFilter out duplicates and expired postings.\n\nEnsure relevance based on the user's preferences.\n\nClearly present the job data in a structured, easy-to-understand format.\n\nInclude essential details such as job title, company, location, job type, summary, and source link.\n\nNotify the user if no results are found, and suggest improvements to the search query if appropriate.\n\nAlways make sure that your searches are purposeful and accurate. You are not a content generator—you are a discovery agent that works through a connected system. Trust the tool, use it properly, and return only what it gives you.\n\nYour goal is to help the user find the best job opportunities efficiently, accurately, and reliably by leveraging the workflow tools built into your environment."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [780, 0], "id": "de122eb9-fa84-4ee8-9c3a-5de16f892324", "name": "AI Agent"}, {"parameters": {"model": "deepseek-r1-distill-llama-70b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [640, 200], "id": "c8fc027c-85a4-4da5-9a11-2e6b4a847e01", "name": "<PERSON><PERSON><PERSON>", "credentials": {"groqApi": {"id": "KWFySKUgUtfeSOzD", "name": "Groq account"}}}, {"parameters": {"tableName": "scout_chat_histories", "contextWindowLength": 100}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [800, 200], "id": "5256eb05-6703-4fa6-820e-d1fa4114987c", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "7NNRIk8hkMrLqp4i", "name": "Postgres account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1.1, "position": [1460, 180], "id": "c8a9dce0-ec1a-47a1-b01d-c4510cece388", "name": "Answer questions with a vector store"}, {"parameters": {"tableName": {"__rl": true, "value": "job_search_database", "mode": "list", "cachedResultName": "job_search_database"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.3, "position": [1300, 360], "id": "4cd8ea6e-ef1c-4602-9427-91e2f6ef29f9", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "cvJe6aBnvDVB655o", "name": "Supabase account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [1200, 520], "id": "6a25e160-031c-4991-ab20-bfba03e37797", "name": "Embeddings <PERSON><PERSON><PERSON>", "credentials": {"ollamaApi": {"id": "RUmxMwRqnis8wiP5", "name": "Ollama account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1640, 340], "id": "9686accd-6e2a-44ed-b84c-ded314de7b7e", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "jVDmIGMg9UvFeaZQ", "name": "OpenAi account"}}}, {"parameters": {"path": "b1a37bcf-979d-4537-b176-c3360fb0f3e3", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [160, 220], "id": "6f25f1ce-9e0d-4497-b62b-116c10d4ad64", "name": "Webhook", "webhookId": "b1a37bcf-979d-4537-b176-c3360fb0f3e3"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Groq Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Answer questions with a vector store": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Supabase Vector Store": {"ai_vectorStore": [[{"node": "Answer questions with a vector store", "type": "ai_vectorStore", "index": 0}]]}, "Embeddings Ollama": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Answer questions with a vector store", "type": "ai_languageModel", "index": 0}]]}, "Webhook": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "54d8aaaa-f72d-48cd-b68e-7e772447efb0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c8210f6b00e31631c22a2f4d410fbe8193ca1dd4b3ba6e91b2301c1e5e3b7e0b"}, "id": "4jn4JiOzQZuclHRD", "tags": [{"createdAt": "2025-06-28T09:29:20.370Z", "updatedAt": "2025-06-28T09:29:20.370Z", "id": "s6x4oD4q2qKmPZdZ", "name": "scout"}]}